<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\LearningPath;
use App\Models\UserLearningPath;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LearningPathController extends Controller
{
    /**
     * Obtenir tous les parcours d'apprentissage
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $language = $request->get('language');
            $level = $request->get('level');
            $category = $request->get('category');
            $userId = 1; // ID utilisateur par défaut

            $query = LearningPath::active();

            if ($language && $language !== 'all') {
                // Mapping des codes de langue
                $languageMapping = [
                    'en' => 'Anglais',
                    'es' => 'Espagnol',
                    'fr' => 'Français',
                    'de' => 'Allemand',
                    'it' => 'Italien'
                ];

                $fullLanguageName = $languageMapping[$language] ?? $language;
                $query->where(function($q) use ($fullLanguageName) {
                    $q->where('language', $fullLanguageName)
                      ->orWhere('language', 'Multilingue');
                });
            }

            if ($level) {
                $query->byLevel($level);
            }

            if ($category) {
                $query->byCategory($category);
            }

            $paths = $query->orderBy('level')
                          ->orderBy('name')
                          ->get();

            // Ajouter les informations de progression pour chaque parcours
            $pathsWithProgress = $paths->map(function ($path) use ($userId) {
                $userPath = UserLearningPath::where('user_id', $userId)
                                           ->where('learning_path_id', $path->id)
                                           ->first();

                return [
                    'id' => $path->id,
                    'name' => $path->name,
                    'language' => $path->language,
                    'description' => $path->description,
                    'level' => $path->level,
                    'category' => $path->category,
                    'estimated_duration_hours' => $path->estimated_duration_hours,
                    'icon' => $path->icon,
                    'color' => $path->color,
                    'completion_reward_xp' => $path->completion_reward_xp,
                    'learning_objectives' => $path->learning_objectives,
                    'prerequisites' => $path->prerequisites,
                    'total_steps' => $path->getTotalSteps(),
                    'is_started' => $userPath !== null,
                    'is_completed' => $userPath && $userPath->completed_at !== null,
                    'completion_percentage' => $userPath ? $userPath->completion_percentage : 0,
                    'xp_earned' => $userPath ? $userPath->total_xp_earned : 0,
                    'next_step' => $userPath ? $userPath->getNextStep() : null
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $pathsWithProgress,
                'message' => 'Parcours d\'apprentissage récupérés avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des parcours',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir un parcours spécifique
     */
    public function show($id): JsonResponse
    {
        try {
            $userId = 1;
            $path = LearningPath::findOrFail($id);

            $userPath = UserLearningPath::where('user_id', $userId)
                                       ->where('learning_path_id', $path->id)
                                       ->first();

            $pathData = [
                'id' => $path->id,
                'name' => $path->name,
                'language' => $path->language,
                'description' => $path->description,
                'level' => $path->level,
                'category' => $path->category,
                'estimated_duration_hours' => $path->estimated_duration_hours,
                'difficulty_progression' => $path->difficulty_progression,
                'icon' => $path->icon,
                'color' => $path->color,
                'completion_reward_xp' => $path->completion_reward_xp,
                'learning_objectives' => $path->learning_objectives,
                'prerequisites' => $path->prerequisites,
                'lesson_sequence' => $path->lesson_sequence,
                'exercise_sequence' => $path->exercise_sequence,
                'total_steps' => $path->getTotalSteps(),
                'is_started' => $userPath !== null,
                'is_completed' => $userPath && $userPath->completed_at !== null,
                'completion_percentage' => $userPath ? $userPath->completion_percentage : 0,
                'xp_earned' => $userPath ? $userPath->total_xp_earned : 0,
                'current_lesson_index' => $userPath ? $userPath->current_lesson_index : 0,
                'current_exercise_index' => $userPath ? $userPath->current_exercise_index : 0,
                'completed_lessons' => $userPath ? $userPath->completed_lessons : [],
                'completed_exercises' => $userPath ? $userPath->completed_exercises : [],
                'next_step' => $userPath ? $userPath->getNextStep() : null,
                'started_at' => $userPath ? $userPath->started_at : null,
                'completed_at' => $userPath ? $userPath->completed_at : null
            ];

            return response()->json([
                'success' => true,
                'data' => $pathData,
                'message' => 'Parcours récupéré avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération du parcours',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Commencer un parcours
     */
    public function start($id): JsonResponse
    {
        try {
            $userId = 1;
            $path = LearningPath::findOrFail($id);

            // Vérifier si le parcours n'est pas déjà commencé
            $existingUserPath = UserLearningPath::where('user_id', $userId)
                                               ->where('learning_path_id', $path->id)
                                               ->first();

            if ($existingUserPath) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ce parcours a déjà été commencé'
                ], 400);
            }

            // Créer l'entrée utilisateur pour ce parcours
            $userPath = UserLearningPath::create([
                'user_id' => $userId,
                'learning_path_id' => $path->id,
                'started_at' => now(),
                'current_lesson_index' => 0,
                'current_exercise_index' => 0,
                'completion_percentage' => 0,
                'total_xp_earned' => 0,
                'is_active' => true
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'user_path_id' => $userPath->id,
                    'next_step' => $userPath->getNextStep()
                ],
                'message' => 'Parcours commencé avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du démarrage du parcours',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Marquer une étape comme terminée
     */
    public function completeStep(Request $request, $id): JsonResponse
    {
        try {
            $request->validate([
                'step_type' => 'required|in:lesson,exercise',
                'step_id' => 'required|integer',
                'xp_earned' => 'integer|min:0'
            ]);

            $userId = 1;
            $userPath = UserLearningPath::where('user_id', $userId)
                                       ->where('learning_path_id', $id)
                                       ->firstOrFail();

            $stepType = $request->step_type;
            $stepId = $request->step_id;
            $xpEarned = $request->xp_earned ?? 0;

            if ($stepType === 'lesson') {
                $userPath->markLessonCompleted($stepId, $xpEarned);
            } else {
                $userPath->markExerciseCompleted($stepId, $xpEarned);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'completion_percentage' => $userPath->completion_percentage,
                    'total_xp_earned' => $userPath->total_xp_earned,
                    'next_step' => $userPath->getNextStep(),
                    'is_completed' => $userPath->completed_at !== null
                ],
                'message' => 'Étape marquée comme terminée'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la completion de l\'étape',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir les statistiques des parcours
     */
    public function getStats(): JsonResponse
    {
        try {
            $userId = 1;

            $stats = [
                'total_paths' => LearningPath::active()->count(),
                'started_paths' => UserLearningPath::where('user_id', $userId)->count(),
                'completed_paths' => UserLearningPath::where('user_id', $userId)->completed()->count(),
                'total_xp_from_paths' => UserLearningPath::where('user_id', $userId)->sum('total_xp_earned'),
                'average_completion' => UserLearningPath::where('user_id', $userId)->avg('completion_percentage') ?? 0,
                'active_paths' => UserLearningPath::where('user_id', $userId)
                                                 ->active()
                                                 ->inProgress()
                                                 ->with('learningPath')
                                                 ->get()
                                                 ->map(function($userPath) {
                                                     return [
                                                         'id' => $userPath->learning_path_id,
                                                         'name' => $userPath->learningPath->name,
                                                         'completion_percentage' => $userPath->completion_percentage,
                                                         'next_step' => $userPath->getNextStep()
                                                     ];
                                                 })
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Statistiques récupérées avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
