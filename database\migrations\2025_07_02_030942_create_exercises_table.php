<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exercises', function (Blueprint $table) {
            $table->id();
            $table->string('language'); // anglais, espagnol, italien, français, allemand
            $table->string('level'); // débutant, intermédiaire, avancé
            $table->string('type'); // vocabulaire, grammaire, écoute, lecture, expression
            $table->string('title');
            $table->text('description')->nullable();
            $table->json('content'); // contenu de l'exercice (questions, réponses, etc.)
            $table->integer('difficulty')->default(1); // 1-5
            $table->integer('points')->default(10); // points gagnés
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['language', 'level', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exercises');
    }
};
