import React from 'react';

function Admin() {
    return (
        <div className="container mx-auto px-4 py-8">
            <div className="max-w-6xl mx-auto">
                <h1 className="text-3xl font-bold text-gray-800 mb-8">
                    Administration Learnify
                </h1>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <h3 className="text-xl font-semibold text-gray-800 mb-4">
                            📚 Gestion des cours
                        </h3>
                        <p className="text-gray-600 mb-4">
                            Ajouter, modifier ou supprimer des cours de langues.
                        </p>
                        <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                            Gérer les cours
                        </button>
                    </div>
                    
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <h3 className="text-xl font-semibold text-gray-800 mb-4">
                            👥 Inscriptions
                        </h3>
                        <p className="text-gray-600 mb-4">
                            Voir et gérer les inscriptions des étudiants.
                        </p>
                        <button className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors">
                            Voir les inscriptions
                        </button>
                    </div>
                    
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <h3 className="text-xl font-semibold text-gray-800 mb-4">
                            📊 Statistiques
                        </h3>
                        <p className="text-gray-600 mb-4">
                            Consulter les statistiques et rapports.
                        </p>
                        <button className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors">
                            Voir les stats
                        </button>
                    </div>
                </div>
                
                <div className="bg-white rounded-lg shadow-md p-8">
                    <h2 className="text-2xl font-semibold text-gray-800 mb-6">
                        Tableau de bord
                    </h2>
                    <div className="text-center text-gray-500 py-8">
                        <p>Interface d'administration en cours de développement...</p>
                        <p className="text-sm mt-2">Les fonctionnalités d'administration seront disponibles une fois la base de données configurée.</p>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Admin;
