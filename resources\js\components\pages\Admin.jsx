import React, { useState, useEffect } from 'react';
import { courseService, registrationService } from '../../services/api';

function Admin() {
    const [activeTab, setActiveTab] = useState('courses');
    const [courses, setCourses] = useState([]);
    const [registrations, setRegistrations] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    // États pour la création/modification de cours
    const [showCourseForm, setShowCourseForm] = useState(false);
    const [editingCourse, setEditingCourse] = useState(null);
    const [courseFormData, setCourseFormData] = useState({
        language: '',
        level: 'Débutant',
        title: '',
        description: '',
        duration_weeks: 8,
        available_spots: 10,
        max_spots: 10,
        start_date: '',
        format: 'Visioconférence',
        price: '',
        is_active: true
    });

    useEffect(() => {
        if (activeTab === 'courses') {
            loadCourses();
        } else if (activeTab === 'registrations') {
            loadRegistrations();
        }
    }, [activeTab]);

    const loadCourses = async () => {
        try {
            setLoading(true);
            const response = await courseService.getCourses();
            if (response.success) {
                setCourses(response.data);
            }
        } catch (err) {
            setError('Erreur lors du chargement des cours');
            console.error('Erreur:', err);
        } finally {
            setLoading(false);
        }
    };

    const loadRegistrations = async () => {
        try {
            setLoading(true);
            const response = await registrationService.getRegistrations();
            if (response.success) {
                setRegistrations(response.data);
            }
        } catch (err) {
            setError('Erreur lors du chargement des inscriptions');
            console.error('Erreur:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleCourseFormChange = (e) => {
        const { name, value, type, checked } = e.target;
        setCourseFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    const handleCourseSubmit = async (e) => {
        e.preventDefault();
        try {
            if (editingCourse) {
                await courseService.updateCourse(editingCourse.id, courseFormData);
            } else {
                await courseService.createCourse(courseFormData);
            }
            setShowCourseForm(false);
            setEditingCourse(null);
            resetCourseForm();
            loadCourses();
        } catch (err) {
            setError('Erreur lors de la sauvegarde du cours');
            console.error('Erreur:', err);
        }
    };

    const resetCourseForm = () => {
        setCourseFormData({
            language: '',
            level: 'Débutant',
            title: '',
            description: '',
            duration_weeks: 8,
            available_spots: 10,
            max_spots: 10,
            start_date: '',
            format: 'Visioconférence',
            price: '',
            is_active: true
        });
    };

    const handleEditCourse = (course) => {
        setEditingCourse(course);
        setCourseFormData({
            language: course.language,
            level: course.level,
            title: course.title,
            description: course.description,
            duration_weeks: course.duration_weeks,
            available_spots: course.available_spots,
            max_spots: course.max_spots,
            start_date: course.start_date,
            format: course.format,
            price: course.price || '',
            is_active: course.is_active
        });
        setShowCourseForm(true);
    };

    const handleDeleteCourse = async (courseId) => {
        if (window.confirm('Êtes-vous sûr de vouloir supprimer ce cours ?')) {
            try {
                await courseService.deleteCourse(courseId);
                loadCourses();
            } catch (err) {
                setError('Erreur lors de la suppression du cours');
                console.error('Erreur:', err);
            }
        }
    };

    const handleUpdateRegistrationStatus = async (registrationId, newStatus) => {
        try {
            await registrationService.updateRegistrationStatus(registrationId, newStatus);
            loadRegistrations();
        } catch (err) {
            setError('Erreur lors de la mise à jour du statut');
            console.error('Erreur:', err);
        }
    };

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-800">Administration Learnify</h1>
                    <p className="text-gray-600 mt-2">Gérez vos cours et inscriptions</p>
                </div>

                {error && (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        <p>{error}</p>
                        <button
                            onClick={() => setError(null)}
                            className="float-right text-red-500 hover:text-red-700"
                        >
                            ×
                        </button>
                    </div>
                )}

                {/* Onglets */}
                <div className="bg-white rounded-lg shadow-md mb-6">
                    <div className="border-b border-gray-200">
                        <nav className="-mb-px flex">
                            <button
                                onClick={() => setActiveTab('courses')}
                                className={`py-4 px-6 border-b-2 font-medium text-sm ${
                                    activeTab === 'courses'
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                Gestion des cours
                            </button>
                            <button
                                onClick={() => setActiveTab('registrations')}
                                className={`py-4 px-6 border-b-2 font-medium text-sm ${
                                    activeTab === 'registrations'
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                Inscriptions
                            </button>
                        </nav>
                    </div>

                    <div className="p-6">
                        {activeTab === 'courses' && (
                            <div>
                                <div className="flex justify-between items-center mb-6">
                                    <h2 className="text-xl font-semibold text-gray-800">Cours</h2>
                                    <button
                                        onClick={() => {
                                            resetCourseForm();
                                            setEditingCourse(null);
                                            setShowCourseForm(true);
                                        }}
                                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                                    >
                                        Nouveau cours
                                    </button>
                                </div>

                                {loading ? (
                                    <div className="text-center py-8">
                                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                                        <p className="text-gray-600">Chargement...</p>
                                    </div>
                                ) : (
                                    <div className="overflow-x-auto">
                                        <table className="min-w-full divide-y divide-gray-200">
                                            <thead className="bg-gray-50">
                                                <tr>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Cours
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Langue/Niveau
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Places
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Date début
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Statut
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Actions
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody className="bg-white divide-y divide-gray-200">
                                                {courses.map(course => (
                                                    <tr key={course.id}>
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <div>
                                                                <div className="text-sm font-medium text-gray-900">
                                                                    {course.title}
                                                                </div>
                                                                <div className="text-sm text-gray-500">
                                                                    {course.format} • {course.duration_weeks} semaines
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <div className="text-sm text-gray-900">{course.language}</div>
                                                            <div className="text-sm text-gray-500">{course.level}</div>
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <div className="text-sm text-gray-900">
                                                                {course.remaining_spots || course.available_spots}/{course.max_spots}
                                                            </div>
                                                            <div className="text-sm text-gray-500">
                                                                {course.registrations_count || 0} inscriptions
                                                            </div>
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                            {course.start_date_formatted || course.start_date}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                                course.is_active !== false
                                                                    ? 'bg-green-100 text-green-800'
                                                                    : 'bg-red-100 text-red-800'
                                                            }`}>
                                                                {course.is_active !== false ? 'Actif' : 'Inactif'}
                                                            </span>
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                            <button
                                                                onClick={() => handleEditCourse(course)}
                                                                className="text-blue-600 hover:text-blue-900 mr-3"
                                                            >
                                                                Modifier
                                                            </button>
                                                            <button
                                                                onClick={() => handleDeleteCourse(course.id)}
                                                                className="text-red-600 hover:text-red-900"
                                                            >
                                                                Supprimer
                                                            </button>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                )}
                            </div>
                        )}

                        {activeTab === 'registrations' && (
                            <div>
                                <h2 className="text-xl font-semibold text-gray-800 mb-6">Inscriptions</h2>

                                {loading ? (
                                    <div className="text-center py-8">
                                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                                        <p className="text-gray-600">Chargement...</p>
                                    </div>
                                ) : (
                                    <div className="overflow-x-auto">
                                        <table className="min-w-full divide-y divide-gray-200">
                                            <thead className="bg-gray-50">
                                                <tr>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Étudiant
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Cours
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Date inscription
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Statut
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Actions
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody className="bg-white divide-y divide-gray-200">
                                                {registrations.map(registration => (
                                                    <tr key={registration.id}>
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <div>
                                                                <div className="text-sm font-medium text-gray-900">
                                                                    {registration.student_name}
                                                                </div>
                                                                <div className="text-sm text-gray-500">
                                                                    {registration.student_email}
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <div className="text-sm text-gray-900">
                                                                {registration.course?.title || 'Cours supprimé'}
                                                            </div>
                                                            <div className="text-sm text-gray-500">
                                                                {registration.course?.language} • {registration.course?.level}
                                                            </div>
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                            {registration.registered_at_formatted || registration.created_at}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <select
                                                                value={registration.status || 'En attente'}
                                                                onChange={(e) => handleUpdateRegistrationStatus(registration.id, e.target.value)}
                                                                className={`text-xs font-semibold rounded-full px-2 py-1 border-0 ${
                                                                    (registration.status || 'En attente') === 'Confirmée' ? 'bg-green-100 text-green-800' :
                                                                    (registration.status || 'En attente') === 'En attente' ? 'bg-yellow-100 text-yellow-800' :
                                                                    'bg-red-100 text-red-800'
                                                                }`}
                                                            >
                                                                <option value="En attente">En attente</option>
                                                                <option value="Confirmée">Confirmée</option>
                                                                <option value="Annulée">Annulée</option>
                                                            </select>
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                            <button
                                                                onClick={() => registrationService.deleteRegistration(registration.id).then(() => loadRegistrations())}
                                                                className="text-red-600 hover:text-red-900"
                                                            >
                                                                Supprimer
                                                            </button>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Modal de création/modification de cours */}
            {showCourseForm && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                {editingCourse ? 'Modifier le cours' : 'Nouveau cours'}
                            </h3>
                            <form onSubmit={handleCourseSubmit} className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Langue *
                                        </label>
                                        <input
                                            type="text"
                                            name="language"
                                            value={courseFormData.language}
                                            onChange={handleCourseFormChange}
                                            required
                                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Ex: Anglais"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Niveau *
                                        </label>
                                        <select
                                            name="level"
                                            value={courseFormData.level}
                                            onChange={handleCourseFormChange}
                                            required
                                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        >
                                            <option value="Débutant">Débutant</option>
                                            <option value="Intermédiaire">Intermédiaire</option>
                                            <option value="Avancé">Avancé</option>
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Titre *
                                    </label>
                                    <input
                                        type="text"
                                        name="title"
                                        value={courseFormData.title}
                                        onChange={handleCourseFormChange}
                                        required
                                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Ex: Anglais pour débutants"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Description *
                                    </label>
                                    <textarea
                                        name="description"
                                        value={courseFormData.description}
                                        onChange={handleCourseFormChange}
                                        required
                                        rows={3}
                                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Description du cours..."
                                    />
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Durée (semaines) *
                                        </label>
                                        <input
                                            type="number"
                                            name="duration_weeks"
                                            value={courseFormData.duration_weeks}
                                            onChange={handleCourseFormChange}
                                            required
                                            min="1"
                                            max="52"
                                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Places disponibles *
                                        </label>
                                        <input
                                            type="number"
                                            name="available_spots"
                                            value={courseFormData.available_spots}
                                            onChange={handleCourseFormChange}
                                            required
                                            min="0"
                                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Places maximum *
                                        </label>
                                        <input
                                            type="number"
                                            name="max_spots"
                                            value={courseFormData.max_spots}
                                            onChange={handleCourseFormChange}
                                            required
                                            min="1"
                                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Date de début *
                                        </label>
                                        <input
                                            type="date"
                                            name="start_date"
                                            value={courseFormData.start_date}
                                            onChange={handleCourseFormChange}
                                            required
                                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Format *
                                        </label>
                                        <select
                                            name="format"
                                            value={courseFormData.format}
                                            onChange={handleCourseFormChange}
                                            required
                                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        >
                                            <option value="Visioconférence">Visioconférence</option>
                                            <option value="Présentiel">Présentiel</option>
                                            <option value="Hybride">Hybride</option>
                                        </select>
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Prix (€) - Cours gratuits
                                        </label>
                                        <input
                                            type="number"
                                            name="price"
                                            value="0.00"
                                            readOnly
                                            className="w-full p-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500"
                                            placeholder="0.00"
                                        />
                                        <p className="text-sm text-green-600 mt-1">✓ Tous les cours sont gratuits</p>
                                    </div>
                                    <div className="flex items-center">
                                        <input
                                            type="checkbox"
                                            name="is_active"
                                            checked={courseFormData.is_active}
                                            onChange={handleCourseFormChange}
                                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        />
                                        <label className="ml-2 block text-sm text-gray-900">
                                            Cours actif
                                        </label>
                                    </div>
                                </div>

                                <div className="flex justify-end space-x-3 pt-4">
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setShowCourseForm(false);
                                            setEditingCourse(null);
                                            resetCourseForm();
                                        }}
                                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                                    >
                                        Annuler
                                    </button>
                                    <button
                                        type="submit"
                                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                                    >
                                        {editingCourse ? 'Mettre à jour' : 'Créer'}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default Admin;
