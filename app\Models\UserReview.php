<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class UserReview extends Model
{
    protected $fillable = [
        'user_id',
        'exercise_id',
        'language',
        'review_count',
        'correct_count',
        'incorrect_count',
        'ease_factor',
        'interval_days',
        'next_review_at',
        'last_reviewed_at',
        'performance_history',
        'is_mastered'
    ];

    protected $casts = [
        'next_review_at' => 'datetime',
        'last_reviewed_at' => 'datetime',
        'performance_history' => 'array',
        'is_mastered' => 'boolean',
        'ease_factor' => 'float'
    ];

    // Relations
    public function exercise()
    {
        return $this->belongsTo(Exercise::class);
    }

    // Méthodes pour l'algorithme de révision espacée (SM-2)
    public function updateReviewData($isCorrect, $quality = null)
    {
        // Quality: 0-5 (0 = incorrect, 5 = perfect)
        if ($quality === null) {
            $quality = $isCorrect ? 4 : 0;
        }

        $this->review_count++;
        $this->last_reviewed_at = now();

        if ($isCorrect) {
            $this->correct_count++;
        } else {
            $this->incorrect_count++;
        }

        // Algorithme SM-2 simplifié
        if ($quality >= 3) {
            // Bonne réponse
            if ($this->review_count === 1) {
                $this->interval_days = 1;
            } elseif ($this->review_count === 2) {
                $this->interval_days = 6;
            } else {
                $this->interval_days = round($this->interval_days * $this->ease_factor);
            }

            // Ajuster le facteur de facilité
            $this->ease_factor = $this->ease_factor + (0.1 - (5 - $quality) * (0.08 + (5 - $quality) * 0.02));
            $this->ease_factor = max(1.3, $this->ease_factor);
        } else {
            // Mauvaise réponse - recommencer
            $this->interval_days = 1;
            $this->ease_factor = max(1.3, $this->ease_factor - 0.2);
        }

        // Calculer la prochaine révision
        $this->next_review_at = Carbon::now()->addDays($this->interval_days);

        // Marquer comme maîtrisé si intervalle > 30 jours et taux de réussite > 80%
        if ($this->interval_days > 30 && $this->getSuccessRate() > 0.8) {
            $this->is_mastered = true;
        }

        // Ajouter à l'historique
        $history = $this->performance_history ?? [];
        $history[] = [
            'date' => now()->toDateString(),
            'quality' => $quality,
            'interval' => $this->interval_days,
            'ease_factor' => $this->ease_factor
        ];
        $this->performance_history = array_slice($history, -20); // Garder les 20 dernières

        $this->save();
    }

    public function getSuccessRate()
    {
        $total = $this->correct_count + $this->incorrect_count;
        return $total > 0 ? $this->correct_count / $total : 0;
    }

    public function isDueForReview()
    {
        return $this->next_review_at === null || $this->next_review_at <= now();
    }

    // Scopes
    public function scopeDueForReview($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('next_review_at')
              ->orWhere('next_review_at', '<=', now());
        });
    }

    public function scopeByLanguage($query, $language)
    {
        return $query->where('language', $language);
    }

    public function scopeNotMastered($query)
    {
        return $query->where('is_mastered', false);
    }
}
