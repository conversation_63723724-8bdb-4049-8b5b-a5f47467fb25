import React from 'react';
import { Link } from 'react-router-dom';

function Footer() {
    return (
        <footer className="bg-gradient-to-r from-gray-900 to-gray-800 text-white py-12 mt-auto">
            <div className="container mx-auto px-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                    {/* About Section */}
                    <div className="md:col-span-2">
                        <div className="flex items-center space-x-2 mb-4">
                            <span className="text-2xl">🎓</span>
                            <h3 className="text-xl font-bold">Learnify</h3>
                        </div>
                        <p className="text-gray-300 mb-4 leading-relaxed">
                            Votre plateforme de référence pour l'apprentissage des langues.
                            Découvrez nos cours interactifs et rejoignez une communauté
                            d'apprenants passionnés du monde entier.
                        </p>
                        <div className="flex space-x-4">
                            <a href="#" className="text-gray-400 hover:text-white transition-colors">
                                <span className="text-xl">📘</span>
                            </a>
                            <a href="#" className="text-gray-400 hover:text-white transition-colors">
                                <span className="text-xl">🐦</span>
                            </a>
                            <a href="#" className="text-gray-400 hover:text-white transition-colors">
                                <span className="text-xl">📷</span>
                            </a>
                            <a href="#" className="text-gray-400 hover:text-white transition-colors">
                                <span className="text-xl">💼</span>
                            </a>
                        </div>
                    </div>

                    {/* Quick Links */}
                    <div>
                        <h3 className="text-lg font-semibold mb-4 text-blue-300">Navigation</h3>
                        <ul className="space-y-3 text-gray-300">
                            <li>
                                <Link to="/" className="hover:text-white transition-colors flex items-center space-x-2">
                                    <span>🏠</span>
                                    <span>Accueil</span>
                                </Link>
                            </li>
                            <li>
                                <Link to="/about" className="hover:text-white transition-colors flex items-center space-x-2">
                                    <span>ℹ️</span>
                                    <span>À propos</span>
                                </Link>
                            </li>
                            <li>
                                <Link to="/admin" className="hover:text-white transition-colors flex items-center space-x-2">
                                    <span>⚙️</span>
                                    <span>Administration</span>
                                </Link>
                            </li>
                        </ul>
                    </div>

                    {/* Contact Info */}
                    <div>
                        <h3 className="text-lg font-semibold mb-4 text-blue-300">Contact</h3>
                        <div className="text-gray-300 space-y-3">
                            <div className="flex items-center space-x-2">
                                <span>📧</span>
                                <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">
                                    <EMAIL>
                                </a>
                            </div>
                            <div className="flex items-center space-x-2">
                                <span>📞</span>
                                <a href="tel:+33123456789" className="hover:text-white transition-colors">
                                    +33 1 23 45 67 89
                                </a>
                            </div>
                            <div className="flex items-center space-x-2">
                                <span>📍</span>
                                <span>Paris, France</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <span>🕒</span>
                                <span>Lun-Ven 9h-18h</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="border-t border-gray-700 mt-8 pt-8">
                    <div className="flex flex-col md:flex-row justify-between items-center text-gray-400 text-sm">
                        <p>&copy; 2025 Learnify. Tous droits réservés.</p>
                        <div className="flex space-x-6 mt-4 md:mt-0">
                            <a href="#" className="hover:text-white transition-colors">Politique de confidentialité</a>
                            <a href="#" className="hover:text-white transition-colors">Conditions d'utilisation</a>
                            <a href="#" className="hover:text-white transition-colors">Cookies</a>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    );
}

export default Footer;
