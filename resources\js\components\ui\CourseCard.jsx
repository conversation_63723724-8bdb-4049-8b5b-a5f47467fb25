import React from 'react';
import { Link } from 'react-router-dom';

function CourseCard({ course }) {
    const getLanguageFlag = (language) => {
        const flags = {
            'Anglais': '🇬🇧',
            'Espagnol': '🇪🇸',
            'Italien': '🇮🇹',
            'Français': '🇫🇷',
            'Allemand': '🇩🇪',
            'Portugais': '🇵🇹',
            'Chinois': '🇨🇳',
            'Japonais': '🇯🇵',
            'Russe': '🇷🇺',
            'Arabe': '🇸🇦'
        };
        return flags[language] || '🌍';
    };

    const getLevelColor = (level) => {
        switch (level) {
            case 'Débutant':
                return 'bg-green-100 text-green-800 border-green-200';
            case 'Intermédiaire':
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case 'Avancé':
                return 'bg-red-100 text-red-800 border-red-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const getFormatIcon = (format) => {
        switch (format) {
            case 'Visioconférence':
                return '💻';
            case 'Présentiel':
                return '🏫';
            case 'Hybride':
                return '🔄';
            default:
                return '📚';
        }
    };

    const spotsPercentage = ((course.available_spots || course.remaining_spots || 0) / (course.max_spots || 1)) * 100;
    const isAlmostFull = spotsPercentage < 30;
    const isFull = spotsPercentage === 0;

    return (
        <div className="group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100 overflow-hidden">
            {/* Header avec gradient */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 text-white">
                <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                        <span className="text-2xl">{getLanguageFlag(course.language)}</span>
                        <h3 className="font-bold text-lg">{course.language}</h3>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-semibold border ${getLevelColor(course.level)}`}>
                        {course.level}
                    </span>
                </div>
                <h4 className="text-sm opacity-90">{course.title}</h4>
            </div>

            {/* Contenu principal */}
            <div className="p-6">
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {course.description}
                </p>

                {/* Informations du cours */}
                <div className="space-y-3 mb-6">
                    <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-2 text-gray-600">
                            <span>{getFormatIcon(course.format)}</span>
                            <span>{course.format}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-gray-600">
                            <span>⏱️</span>
                            <span>{course.duration_weeks} semaines</span>
                        </div>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-2 text-gray-600">
                            <span>📅</span>
                            <span>{course.start_date_formatted || course.start_date}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-green-600 font-semibold">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            <span>GRATUIT</span>
                        </div>
                    </div>
                </div>

                {/* Barre de progression des places */}
                <div className="mb-4">
                    <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-gray-600">Places disponibles</span>
                        <span className={`text-sm font-semibold ${
                            isFull ? 'text-red-600' : 
                            isAlmostFull ? 'text-orange-600' : 
                            'text-green-600'
                        }`}>
                            {course.available_spots || course.remaining_spots || 0}/{course.max_spots}
                        </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                            className={`h-2 rounded-full transition-all duration-300 ${
                                isFull ? 'bg-red-500' : 
                                isAlmostFull ? 'bg-orange-500' : 
                                'bg-green-500'
                            }`}
                            style={{ width: `${spotsPercentage}%` }}
                        ></div>
                    </div>
                    {isFull && (
                        <p className="text-red-600 text-xs mt-1 font-medium">Complet</p>
                    )}
                    {isAlmostFull && !isFull && (
                        <p className="text-orange-600 text-xs mt-1 font-medium">Dernières places !</p>
                    )}
                </div>

                {/* Bouton d'action */}
                <Link
                    to={`/course/${course.id}`}
                    className={`block w-full text-center py-3 px-4 rounded-lg font-semibold transition-all duration-200 ${
                        isFull 
                            ? 'bg-gray-100 text-gray-500 cursor-not-allowed' 
                            : 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 transform hover:scale-105'
                    }`}
                >
                    {isFull ? 'Complet' : 'Voir les détails'}
                </Link>
            </div>

            {/* Badge de statut */}
            {course.is_active !== false && (
                <div className="absolute top-2 right-2">
                    <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-semibold">
                        Actif
                    </span>
                </div>
            )}
        </div>
    );
}

export default CourseCard;
