<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserReview;
use App\Models\Exercise;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ReviewController extends Controller
{
    /**
     * Obtenir les exercices dus pour révision
     */
    public function getDueReviews($language): JsonResponse
    {
        try {
            $userId = 1; // ID utilisateur par défaut

            // Mapping des codes de langue vers les noms complets
            $languageMapping = [
                'en' => 'Anglais',
                'es' => 'Espagnol',
                'fr' => 'Français',
                'de' => 'Allemand',
                'it' => 'Italien',
                'pt' => 'Portugais',
                'zh' => 'Chinois',
                'ja' => 'Japonais'
            ];

            $fullLanguageName = $languageMapping[$language] ?? $language;

            // Récupérer les révisions dues
            $dueReviews = UserReview::with('exercise')
                ->where('user_id', $userId)
                ->byLanguage($fullLanguageName)
                ->dueForReview()
                ->notMastered()
                ->orderBy('next_review_at')
                ->limit(10) // Limiter à 10 exercices par session
                ->get();

            // Si pas assez de révisions, ajouter de nouveaux exercices
            if ($dueReviews->count() < 5) {
                $existingExerciseIds = UserReview::where('user_id', $userId)
                    ->byLanguage($fullLanguageName)
                    ->pluck('exercise_id')
                    ->toArray();

                $newExercises = Exercise::where('language', $fullLanguageName)
                    ->where('is_active', true)
                    ->whereNotIn('id', $existingExerciseIds)
                    ->orderBy('difficulty')
                    ->limit(5 - $dueReviews->count())
                    ->get();

                // Créer des entrées de révision pour les nouveaux exercices
                foreach ($newExercises as $exercise) {
                    $review = UserReview::create([
                        'user_id' => $userId,
                        'exercise_id' => $exercise->id,
                        'language' => $fullLanguageName,
                        'next_review_at' => now()
                    ]);
                    $review->load('exercise');
                    $dueReviews->push($review);
                }
            }

            // Formater les données pour l'interface
            $formattedReviews = $dueReviews->map(function ($review) {
                $exercise = $review->exercise;
                $content = $exercise->content;
                $exerciseType = $exercise->exercise_type ?? 'multiple_choice';

                // Adapter le format du contenu
                if (isset($content['questions']) && is_array($content['questions'])) {
                    $firstQuestion = $content['questions'][0];
                    $content = [
                        'question' => $firstQuestion['question'],
                        'options' => $firstQuestion['options'],
                        'correct_answer' => $firstQuestion['options'][$firstQuestion['correct']],
                        'explanation' => 'Bonne réponse !'
                    ];
                }

                return [
                    'review_id' => $review->id,
                    'exercise_id' => $exercise->id,
                    'title' => $exercise->title,
                    'exercise_type' => $exerciseType,
                    'content' => $content,
                    'points' => $exercise->points,
                    'difficulty' => $exercise->difficulty,
                    'review_count' => $review->review_count,
                    'success_rate' => round($review->getSuccessRate() * 100, 1),
                    'interval_days' => $review->interval_days,
                    'is_new' => $review->review_count === 0
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedReviews,
                'message' => "Révisions pour {$fullLanguageName} récupérées avec succès"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des révisions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Soumettre une réponse de révision
     */
    public function submitReview(Request $request, $reviewId): JsonResponse
    {
        try {
            $request->validate([
                'is_correct' => 'required|boolean',
                'quality' => 'nullable|integer|min:0|max:5'
            ]);

            $review = UserReview::findOrFail($reviewId);
            $review->updateReviewData(
                $request->is_correct,
                $request->quality
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'next_review_in_days' => $review->interval_days,
                    'success_rate' => round($review->getSuccessRate() * 100, 1),
                    'is_mastered' => $review->is_mastered,
                    'review_count' => $review->review_count
                ],
                'message' => 'Révision enregistrée avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'enregistrement de la révision',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Statistiques de révision
     */
    public function getReviewStats($language): JsonResponse
    {
        try {
            $userId = 1;

            $languageMapping = [
                'en' => 'Anglais',
                'es' => 'Espagnol',
                'fr' => 'Français',
                'de' => 'Allemand',
                'it' => 'Italien',
                'pt' => 'Portugais',
                'zh' => 'Chinois',
                'ja' => 'Japonais'
            ];

            $fullLanguageName = $languageMapping[$language] ?? $language;

            $stats = [
                'total_reviews' => UserReview::where('user_id', $userId)->byLanguage($fullLanguageName)->count(),
                'due_today' => UserReview::where('user_id', $userId)->byLanguage($fullLanguageName)->dueForReview()->count(),
                'mastered' => UserReview::where('user_id', $userId)->byLanguage($fullLanguageName)->where('is_mastered', true)->count(),
                'average_success_rate' => UserReview::where('user_id', $userId)->byLanguage($fullLanguageName)->get()->avg(function($review) {
                    return $review->getSuccessRate();
                }) * 100
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Statistiques récupérées avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
