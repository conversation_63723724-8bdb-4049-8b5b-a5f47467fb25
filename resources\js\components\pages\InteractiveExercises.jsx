import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

const InteractiveExercises = () => {
    const { language } = useParams();
    const navigate = useNavigate();
    const [exercises, setExercises] = useState([]);
    const [currentExercise, setCurrentExercise] = useState(null);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [userAnswer, setUserAnswer] = useState('');
    const [selectedOption, setSelectedOption] = useState('');
    const [showResult, setShowResult] = useState(false);
    const [isCorrect, setIsCorrect] = useState(false);
    const [score, setScore] = useState(0);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [matchingPairs, setMatchingPairs] = useState({});

    const languageNames = {
        'en': 'Anglais',
        'es': 'Espagnol', 
        'fr': 'Français',
        'de': 'Allemand',
        'it': 'Italien',
        'pt': 'Portugais',
        'zh': 'Chinois',
        'ja': 'Japonais'
    };

    const languageFlags = {
        'en': '🇬🇧',
        'es': '🇪🇸',
        'fr': '🇫🇷', 
        'de': '🇩🇪',
        'it': '🇮🇹',
        'pt': '🇵🇹',
        'zh': '🇨🇳',
        'ja': '🇯🇵'
    };

    useEffect(() => {
        if (language) {
            fetchExercises();
        }
    }, [language]);

    useEffect(() => {
        if (exercises.length > 0) {
            setCurrentExercise(exercises[currentIndex]);
            resetExerciseState();
        }
    }, [exercises, currentIndex]);

    const fetchExercises = async () => {
        try {
            console.log('Fetching exercises for language:', language);
            const response = await fetch(`/api/exercises/language/${language}`);
            console.log('Response status:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('API Response:', data);

            if (data.success) {
                setExercises(data.data);
                console.log('Exercises loaded:', data.data.length);
            } else {
                setError('Aucun exercice trouvé');
            }
        } catch (error) {
            console.error('Erreur lors du chargement des exercices:', error);
            setError('Erreur de chargement: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    const resetExerciseState = () => {
        setUserAnswer('');
        setSelectedOption('');
        setShowResult(false);
        setIsCorrect(false);
        setMatchingPairs({});
    };

    const checkAnswer = () => {
        if (!currentExercise) return;

        let correct = false;
        const content = currentExercise.content;

        switch (currentExercise.exercise_type) {
            case 'multiple_choice':
                correct = selectedOption === content.correct_answer;
                break;
            case 'translation':
                const userAnswerLower = userAnswer.toLowerCase().trim();
                const correctAnswer = content.correct_answer.toLowerCase();
                const alternatives = content.alternatives || [];
                correct = userAnswerLower === correctAnswer || 
                         alternatives.some(alt => alt.toLowerCase() === userAnswerLower);
                break;
            case 'fill_blank':
                correct = userAnswer.toLowerCase().trim() === content.correct_answer.toLowerCase();
                break;
            case 'listening':
                correct = userAnswer.toLowerCase().trim() === content.correct_answer.toLowerCase();
                break;
            case 'matching':
                const pairs = content.pairs;
                correct = pairs.every(pair => matchingPairs[pair.left] === pair.right);
                break;
            default:
                correct = false;
        }

        setIsCorrect(correct);
        setShowResult(true);
        
        if (correct) {
            setScore(score + currentExercise.points);
        }
    };

    const nextExercise = () => {
        if (currentIndex < exercises.length - 1) {
            setCurrentIndex(currentIndex + 1);
        } else {
            // Fin des exercices
            alert(`Exercices terminés ! Score final: ${score} points`);
            navigate(`/learn/${language}`);
        }
    };

    const handleMatchingClick = (left, right) => {
        setMatchingPairs(prev => ({
            ...prev,
            [left]: right
        }));
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                <div className="text-white text-2xl">Chargement des exercices...</div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-red-500 to-pink-600 flex items-center justify-center">
                <div className="text-center text-white">
                    <div className="text-6xl mb-4">❌</div>
                    <h1 className="text-4xl font-bold mb-4">Erreur</h1>
                    <p className="text-xl mb-8">{error}</p>
                    <button
                        onClick={() => {
                            setError(null);
                            setLoading(true);
                            fetchExercises();
                        }}
                        className="bg-white/20 backdrop-blur-sm text-white px-8 py-4 rounded-full font-semibold hover:bg-white/30 transition-all"
                    >
                        Réessayer
                    </button>
                </div>
            </div>
        );
    }

    if (exercises.length === 0) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                <div className="text-center text-white">
                    <div className="text-6xl mb-4">📝</div>
                    <h1 className="text-4xl font-bold mb-4">Aucun exercice disponible</h1>
                    <p className="text-xl mb-8">Les exercices pour {languageNames[language]} seront bientôt disponibles !</p>
                    <button 
                        onClick={() => navigate(`/learn/${language}`)}
                        className="bg-white/20 backdrop-blur-sm text-white px-8 py-4 rounded-full font-semibold hover:bg-white/30 transition-all"
                    >
                        ← Retour au dashboard
                    </button>
                </div>
            </div>
        );
    }

    if (!currentExercise) return null;

    const renderExerciseContent = () => {
        const content = currentExercise.content;

        switch (currentExercise.exercise_type) {
            case 'multiple_choice':
                return (
                    <div className="space-y-6">
                        <div className="text-center">
                            <h2 className="text-3xl font-bold text-white mb-4">{content.question}</h2>
                            {currentExercise.instruction && (
                                <p className="text-white/80 text-lg">{currentExercise.instruction}</p>
                            )}
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {content.options.map((option, index) => (
                                <button
                                    key={index}
                                    onClick={() => setSelectedOption(option)}
                                    className={`p-6 rounded-2xl text-lg font-semibold transition-all ${
                                        selectedOption === option
                                            ? 'bg-blue-500 text-white transform scale-105'
                                            : 'bg-white/20 text-white hover:bg-white/30'
                                    }`}
                                    disabled={showResult}
                                >
                                    {option}
                                </button>
                            ))}
                        </div>
                    </div>
                );

            case 'translation':
                return (
                    <div className="space-y-6">
                        <div className="text-center">
                            <h2 className="text-3xl font-bold text-white mb-4">
                                Traduisez : "{content.text_to_translate}"
                            </h2>
                            {currentExercise.instruction && (
                                <p className="text-white/80 text-lg">{currentExercise.instruction}</p>
                            )}
                        </div>
                        <div className="max-w-md mx-auto">
                            <input
                                type="text"
                                value={userAnswer}
                                onChange={(e) => setUserAnswer(e.target.value)}
                                placeholder="Votre traduction..."
                                className="w-full p-4 rounded-2xl text-lg text-center border-none outline-none"
                                disabled={showResult}
                                onKeyPress={(e) => e.key === 'Enter' && !showResult && checkAnswer()}
                            />
                            {currentExercise.hint && !showResult && (
                                <p className="text-white/60 text-sm mt-2 text-center">
                                    💡 {currentExercise.hint}
                                </p>
                            )}
                        </div>
                    </div>
                );

            case 'fill_blank':
                return (
                    <div className="space-y-6">
                        <div className="text-center">
                            <h2 className="text-3xl font-bold text-white mb-4">
                                Complétez la phrase
                            </h2>
                            <div className="text-2xl text-white mb-4">
                                {content.sentence.replace('_____', 
                                    `___${userAnswer || '?'}___`
                                )}
                            </div>
                        </div>
                        <div className="max-w-md mx-auto">
                            <input
                                type="text"
                                value={userAnswer}
                                onChange={(e) => setUserAnswer(e.target.value)}
                                placeholder="Mot manquant..."
                                className="w-full p-4 rounded-2xl text-lg text-center border-none outline-none"
                                disabled={showResult}
                                onKeyPress={(e) => e.key === 'Enter' && !showResult && checkAnswer()}
                            />
                        </div>
                        {content.options && (
                            <div className="flex justify-center space-x-4">
                                {content.options.map((option, index) => (
                                    <button
                                        key={index}
                                        onClick={() => setUserAnswer(option)}
                                        className="bg-white/20 text-white px-6 py-3 rounded-full hover:bg-white/30 transition-all"
                                        disabled={showResult}
                                    >
                                        {option}
                                    </button>
                                ))}
                            </div>
                        )}
                    </div>
                );

            case 'matching':
                return (
                    <div className="space-y-6">
                        <div className="text-center">
                            <h2 className="text-3xl font-bold text-white mb-4">
                                Associez les éléments
                            </h2>
                        </div>
                        <div className="grid grid-cols-2 gap-8 max-w-4xl mx-auto">
                            <div className="space-y-4">
                                <h3 className="text-xl font-bold text-white text-center">À associer</h3>
                                {content.pairs.map((pair, index) => (
                                    <button
                                        key={index}
                                        onClick={() => handleMatchingClick(pair.left, pair.right)}
                                        className={`w-full p-4 rounded-2xl text-lg font-semibold transition-all ${
                                            matchingPairs[pair.left] === pair.right
                                                ? 'bg-green-500 text-white'
                                                : 'bg-white/20 text-white hover:bg-white/30'
                                        }`}
                                        disabled={showResult}
                                    >
                                        {pair.left}
                                    </button>
                                ))}
                            </div>
                            <div className="space-y-4">
                                <h3 className="text-xl font-bold text-white text-center">Réponses</h3>
                                {content.pairs.map((pair, index) => (
                                    <div
                                        key={index}
                                        className={`w-full p-4 rounded-2xl text-lg font-semibold text-center ${
                                            Object.values(matchingPairs).includes(pair.right)
                                                ? 'bg-blue-500 text-white'
                                                : 'bg-white/10 text-white/60'
                                        }`}
                                    >
                                        {pair.right}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                );

            case 'listening':
                return (
                    <div className="space-y-6">
                        <div className="text-center">
                            <h2 className="text-3xl font-bold text-white mb-4">
                                Écoutez et écrivez
                            </h2>
                            <button className="bg-blue-500 hover:bg-blue-600 text-white p-6 rounded-full text-4xl mb-6">
                                🔊
                            </button>
                            <p className="text-white/80">Cliquez pour écouter l'audio</p>
                        </div>
                        <div className="max-w-md mx-auto">
                            <input
                                type="text"
                                value={userAnswer}
                                onChange={(e) => setUserAnswer(e.target.value)}
                                placeholder="Ce que vous entendez..."
                                className="w-full p-4 rounded-2xl text-lg text-center border-none outline-none"
                                disabled={showResult}
                                onKeyPress={(e) => e.key === 'Enter' && !showResult && checkAnswer()}
                            />
                        </div>
                    </div>
                );

            default:
                return (
                    <div className="text-center text-white">
                        <p>Type d'exercice non supporté: {currentExercise.exercise_type}</p>
                    </div>
                );
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-purple-500 to-pink-600">
            <div className="max-w-6xl mx-auto px-4 py-8">
                {/* Header avec progression */}
                <div className="text-center mb-8">
                    <div className="flex items-center justify-between mb-6">
                        <button 
                            onClick={() => navigate(`/learn/${language}`)}
                            className="bg-white/20 backdrop-blur-sm text-white px-6 py-3 rounded-full font-semibold hover:bg-white/30 transition-all"
                        >
                            ← Retour
                        </button>
                        <div className="text-white">
                            <span className="text-2xl font-bold">{currentIndex + 1}</span>
                            <span className="text-lg opacity-80"> / {exercises.length}</span>
                        </div>
                        <div className="text-white font-bold">
                            🏆 {score} points
                        </div>
                    </div>
                    
                    {/* Barre de progression */}
                    <div className="w-full bg-white/20 rounded-full h-3 mb-6">
                        <div 
                            className="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full transition-all duration-500"
                            style={{ width: `${((currentIndex + 1) / exercises.length) * 100}%` }}
                        ></div>
                    </div>

                    <div className="text-6xl mb-4">{languageFlags[language]}</div>
                    <h1 className="text-4xl font-bold text-white mb-2">
                        Exercices d'{languageNames[language]}
                    </h1>
                </div>

                {/* Contenu de l'exercice */}
                <div className="bg-white/20 backdrop-blur-sm rounded-3xl p-8 mb-8">
                    {renderExerciseContent()}
                </div>

                {/* Résultat et navigation */}
                {showResult && (
                    <div className={`text-center p-6 rounded-2xl mb-8 ${
                        isCorrect ? 'bg-green-500/20' : 'bg-red-500/20'
                    }`}>
                        <div className="text-6xl mb-4">
                            {isCorrect ? '✅' : '❌'}
                        </div>
                        <h3 className={`text-2xl font-bold mb-2 ${
                            isCorrect ? 'text-green-100' : 'text-red-100'
                        }`}>
                            {isCorrect ? 'Correct !' : 'Incorrect'}
                        </h3>
                        {currentExercise.content.explanation && (
                            <p className="text-white/80 mb-4">
                                {currentExercise.content.explanation}
                            </p>
                        )}
                        {isCorrect && (
                            <p className="text-green-100">
                                +{currentExercise.points} points !
                            </p>
                        )}
                    </div>
                )}

                {/* Boutons d'action */}
                <div className="text-center">
                    {!showResult ? (
                        <button 
                            onClick={checkAnswer}
                            className="bg-green-500 hover:bg-green-600 text-white px-12 py-4 rounded-full text-xl font-bold transform hover:scale-105 transition-all duration-300 shadow-lg"
                            disabled={
                                (currentExercise.exercise_type === 'multiple_choice' && !selectedOption) ||
                                (['translation', 'fill_blank', 'listening'].includes(currentExercise.exercise_type) && !userAnswer.trim()) ||
                                (currentExercise.exercise_type === 'matching' && Object.keys(matchingPairs).length < currentExercise.content.pairs.length)
                            }
                        >
                            Vérifier
                        </button>
                    ) : (
                        <button 
                            onClick={nextExercise}
                            className="bg-blue-500 hover:bg-blue-600 text-white px-12 py-4 rounded-full text-xl font-bold transform hover:scale-105 transition-all duration-300 shadow-lg"
                        >
                            {currentIndex < exercises.length - 1 ? 'Suivant →' : 'Terminer'}
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default InteractiveExercises;
