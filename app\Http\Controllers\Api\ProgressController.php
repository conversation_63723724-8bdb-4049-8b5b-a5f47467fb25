<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\UserProgress;
use App\Models\Lesson;
use Illuminate\Http\JsonResponse;

class ProgressController extends Controller
{
    public function getProgress($language): JsonResponse
    {
        // Pour l'instant, on simule un utilisateur (ID 1)
        $userId = 1;

        $progress = UserProgress::where('user_id', $userId)
            ->where('language', $language)
            ->first();

        if (!$progress) {
            $progress = UserProgress::create([
                'user_id' => $userId,
                'language' => $language,
                'current_level' => 1,
                'total_xp' => 0,
                'daily_xp' => 0,
                'streak_days' => 0,
                'completed_lessons' => [],
                'achievements' => []
            ]);
        }

        // Calculer les statistiques
        $totalLessons = Lesson::where('language', $language)->count();
        $completedLessons = count($progress->completed_lessons ?? []);
        $completionPercentage = $totalLessons > 0 ? round(($completedLessons / $totalLessons) * 100) : 0;

        // XP nécessaire pour le niveau suivant
        $xpForNextLevel = $progress->current_level * 100;
        $xpProgress = $progress->total_xp % 100;

        return response()->json([
            'success' => true,
            'data' => [
                'progress' => $progress,
                'stats' => [
                    'total_lessons' => $totalLessons,
                    'completed_lessons' => $completedLessons,
                    'completion_percentage' => $completionPercentage,
                    'xp_for_next_level' => $xpForNextLevel,
                    'xp_progress' => $xpProgress,
                    'level_progress_percentage' => round(($xpProgress / 100) * 100)
                ]
            ],
            'message' => 'Progression récupérée avec succès'
        ]);
    }

    public function getAllProgress(): JsonResponse
    {
        $userId = 1;

        $allProgress = UserProgress::where('user_id', $userId)->get();

        $progressData = $allProgress->map(function ($progress) {
            $totalLessons = Lesson::where('language', $progress->language)->count();
            $completedLessons = count($progress->completed_lessons ?? []);
            $completionPercentage = $totalLessons > 0 ? round(($completedLessons / $totalLessons) * 100) : 0;

            return [
                'language' => $progress->language,
                'level' => $progress->current_level,
                'total_xp' => $progress->total_xp,
                'streak_days' => $progress->streak_days,
                'completion_percentage' => $completionPercentage,
                'last_activity' => $progress->last_activity_date,
                'achievements_count' => count($progress->achievements ?? [])
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $progressData,
            'message' => 'Toutes les progressions récupérées avec succès'
        ]);
    }

    public function getAchievements($language): JsonResponse
    {
        $userId = 1;

        $progress = UserProgress::where('user_id', $userId)
            ->where('language', $language)
            ->first();

        $achievements = $progress ? $progress->achievements : [];

        // Définir tous les achievements possibles
        $allAchievements = [
            'first_lesson' => ['name' => 'Première leçon', 'description' => 'Terminer votre première leçon', 'icon' => '🎯'],
            'level_2' => ['name' => 'Niveau 2', 'description' => 'Atteindre le niveau 2', 'icon' => '⭐'],
            'level_3' => ['name' => 'Niveau 3', 'description' => 'Atteindre le niveau 3', 'icon' => '🌟'],
            'level_5' => ['name' => 'Niveau 5', 'description' => 'Atteindre le niveau 5', 'icon' => '💫'],
            'streak_7' => ['name' => 'Semaine parfaite', 'description' => '7 jours consécutifs', 'icon' => '🔥'],
            'streak_30' => ['name' => 'Mois parfait', 'description' => '30 jours consécutifs', 'icon' => '🏆'],
            'xp_100' => ['name' => 'Centurion', 'description' => 'Gagner 100 XP', 'icon' => '💯'],
            'xp_500' => ['name' => 'Expert', 'description' => 'Gagner 500 XP', 'icon' => '🎖️']
        ];

        $achievementData = array_map(function ($key) use ($allAchievements, $achievements) {
            return [
                'key' => $key,
                'name' => $allAchievements[$key]['name'],
                'description' => $allAchievements[$key]['description'],
                'icon' => $allAchievements[$key]['icon'],
                'unlocked' => in_array($key, $achievements)
            ];
        }, array_keys($allAchievements));

        return response()->json([
            'success' => true,
            'data' => $achievementData,
            'message' => 'Achievements récupérés avec succès'
        ]);
    }
}
