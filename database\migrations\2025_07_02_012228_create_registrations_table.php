<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('registrations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('course_id')->constrained()->onDelete('cascade');
            $table->string('student_name'); // Nom de l'étudiant
            $table->string('student_email'); // Email de l'étudiant
            $table->string('student_phone')->nullable(); // Téléphone (optionnel)
            $table->text('motivation')->nullable(); // Motivation pour suivre le cours
            $table->enum('status', ['En attente', 'Confirmée', 'Annulée'])->default('En attente');
            $table->timestamp('registered_at')->useCurrent(); // Date d'inscription
            $table->timestamps();

            // Index pour optimiser les requêtes
            $table->index(['course_id', 'status']);
            $table->index('student_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('registrations');
    }
};
