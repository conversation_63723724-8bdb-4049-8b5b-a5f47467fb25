<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Course;
use Carbon\Carbon;

class UpdateCourseStartDatesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $courses = Course::whereNull('start_date')->get();

        foreach ($courses as $index => $course) {
            // Ajouter des dates de début échelonnées dans le futur
            $startDate = Carbon::now()->addWeeks($index + 1);
            $course->update(['start_date' => $startDate]);
        }

        $this->command->info('Dates de début mises à jour pour ' . $courses->count() . ' cours.');
    }
}
