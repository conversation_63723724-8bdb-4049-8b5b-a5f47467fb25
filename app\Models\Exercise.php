<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Exercise extends Model
{
    use HasFactory;

    protected $fillable = [
        'language',
        'level',
        'type',
        'title',
        'description',
        'content',
        'difficulty',
        'points',
        'is_active'
    ];

    protected $casts = [
        'content' => 'array',
        'is_active' => 'boolean'
    ];

    // Relation avec le progrès des utilisateurs
    public function userProgress()
    {
        return $this->hasMany(UserExerciseProgress::class);
    }

    // Scope pour filtrer par langue
    public function scopeByLanguage($query, $language)
    {
        return $query->where('language', $language);
    }

    // Scope pour filtrer par niveau
    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    // Scope pour filtrer par type
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Scope pour les exercices actifs
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
