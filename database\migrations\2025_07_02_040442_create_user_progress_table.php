<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_progress', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('language'); // Langue étudiée
            $table->integer('current_level')->default(1); // Niveau actuel
            $table->integer('total_xp')->default(0); // XP total
            $table->integer('daily_xp')->default(0); // XP du jour
            $table->integer('streak_days')->default(0); // Jours consécutifs
            $table->date('last_activity_date')->nullable(); // Dernière activité
            $table->json('completed_lessons')->nullable(); // IDs des leçons terminées
            $table->json('achievements')->nullable(); // Badges obtenus
            $table->timestamps();

            $table->unique(['user_id', 'language']);
            $table->index(['user_id', 'language']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_progress');
    }
};
