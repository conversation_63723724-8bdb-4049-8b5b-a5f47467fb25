import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

const LearningPaths = () => {
    const { language } = useParams();
    const navigate = useNavigate();
    const [paths, setPaths] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedLevel, setSelectedLevel] = useState('all');
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [stats, setStats] = useState({});

    const languageNames = {
        'en': 'Anglais',
        'es': 'Espagnol', 
        'fr': 'Français',
        'de': 'Allemand',
        'it': 'Italien',
        'all': 'Toutes les langues'
    };

    const languageFlags = {
        'en': '🇬🇧',
        'es': '🇪🇸',
        'fr': '🇫🇷', 
        'de': '🇩🇪',
        'it': '🇮🇹',
        'all': '🌍'
    };

    useEffect(() => {
        fetchPaths();
        fetchStats();
    }, [language, selectedLevel, selectedCategory]);

    const fetchPaths = async () => {
        try {
            const params = new URLSearchParams();
            if (language && language !== 'all') params.append('language', language);
            if (selectedLevel !== 'all') params.append('level', selectedLevel);
            if (selectedCategory !== 'all') params.append('category', selectedCategory);

            const response = await fetch(`/api/learning-paths?${params}`);
            const data = await response.json();
            if (data.success) {
                setPaths(data.data);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des parcours:', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchStats = async () => {
        try {
            const response = await fetch('/api/learning-paths-stats');
            const data = await response.json();
            if (data.success) {
                setStats(data.data);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des statistiques:', error);
        }
    };

    const startPath = async (pathId) => {
        try {
            const response = await fetch(`/api/learning-paths/${pathId}/start`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            const data = await response.json();
            if (data.success) {
                fetchPaths(); // Rafraîchir la liste
                // Rediriger vers la première étape
                if (data.data.next_step) {
                    const step = data.data.next_step;
                    if (step.type === 'lesson') {
                        navigate(`/learn/${language}/lessons/${step.id}`);
                    } else if (step.type === 'exercise') {
                        navigate(`/learn/${language}/exercises`);
                    }
                }
            } else {
                alert(data.message);
            }
        } catch (error) {
            console.error('Erreur lors du démarrage du parcours:', error);
        }
    };

    const continuePath = (path) => {
        if (path.next_step) {
            const step = path.next_step;
            if (step.type === 'lesson') {
                navigate(`/learn/${language}/lessons/${step.id}`);
            } else if (step.type === 'exercise') {
                navigate(`/learn/${language}/exercises`);
            }
        }
    };

    const getLevelColor = (level) => {
        switch (level) {
            case 'Débutant': return 'bg-green-500';
            case 'Intermédiaire': return 'bg-yellow-500';
            case 'Avancé': return 'bg-red-500';
            default: return 'bg-blue-500';
        }
    };

    const getCategoryIcon = (category) => {
        switch (category) {
            case 'Général': return '📚';
            case 'Grammaire': return '📝';
            case 'Vocabulaire': return '💭';
            case 'Conversation': return '💬';
            case 'Révision': return '🔄';
            default: return '📖';
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-pink-600 flex items-center justify-center">
                <div className="text-white text-2xl">Chargement des parcours...</div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-pink-600 p-4">
            {/* Header */}
            <div className="max-w-7xl mx-auto mb-8">
                <div className="flex items-center justify-between bg-white/10 backdrop-blur-lg rounded-2xl p-6">
                    <div className="flex items-center space-x-4">
                        <button
                            onClick={() => navigate('/')}
                            className="text-white hover:text-white/80 text-2xl"
                        >
                            ←
                        </button>
                        <div className="flex items-center space-x-3">
                            <span className="text-4xl">{languageFlags[language || 'all']}</span>
                            <div>
                                <h1 className="text-3xl font-bold text-white">Parcours d'apprentissage</h1>
                                <p className="text-white/80">{languageNames[language || 'all']}</p>
                            </div>
                        </div>
                    </div>
                    
                    {/* Stats rapides */}
                    <div className="flex space-x-6 text-white">
                        <div className="text-center">
                            <div className="text-2xl font-bold">{stats.started_paths || 0}</div>
                            <div className="text-sm opacity-80">Commencés</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold">{stats.completed_paths || 0}</div>
                            <div className="text-sm opacity-80">Terminés</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold">{stats.total_xp_from_paths || 0}</div>
                            <div className="text-sm opacity-80">XP Total</div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Filtres */}
            <div className="max-w-7xl mx-auto mb-8">
                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
                    <div className="flex flex-wrap gap-4">
                        <div>
                            <label className="block text-white text-sm font-medium mb-2">Niveau</label>
                            <select
                                value={selectedLevel}
                                onChange={(e) => setSelectedLevel(e.target.value)}
                                className="bg-white/20 text-white rounded-lg px-4 py-2 border border-white/30 focus:border-white/50 focus:outline-none"
                            >
                                <option value="all">Tous les niveaux</option>
                                <option value="Débutant">Débutant</option>
                                <option value="Intermédiaire">Intermédiaire</option>
                                <option value="Avancé">Avancé</option>
                                <option value="Tous niveaux">Tous niveaux</option>
                            </select>
                        </div>
                        
                        <div>
                            <label className="block text-white text-sm font-medium mb-2">Catégorie</label>
                            <select
                                value={selectedCategory}
                                onChange={(e) => setSelectedCategory(e.target.value)}
                                className="bg-white/20 text-white rounded-lg px-4 py-2 border border-white/30 focus:border-white/50 focus:outline-none"
                            >
                                <option value="all">Toutes les catégories</option>
                                <option value="Général">Général</option>
                                <option value="Grammaire">Grammaire</option>
                                <option value="Vocabulaire">Vocabulaire</option>
                                <option value="Conversation">Conversation</option>
                                <option value="Révision">Révision</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            {/* Parcours actifs */}
            {stats.active_paths && stats.active_paths.length > 0 && (
                <div className="max-w-7xl mx-auto mb-8">
                    <h2 className="text-2xl font-bold text-white mb-4">Parcours en cours</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {stats.active_paths.map((activePath) => {
                            const fullPath = paths.find(p => p.id === activePath.id);
                            if (!fullPath) return null;
                            
                            return (
                                <div key={activePath.id} className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border-2 border-yellow-400/50">
                                    <div className="flex items-center justify-between mb-4">
                                        <span className="text-3xl">{fullPath.icon}</span>
                                        <span className="bg-yellow-400/80 text-black px-3 py-1 rounded-full text-sm font-bold">
                                            En cours
                                        </span>
                                    </div>
                                    
                                    <h3 className="text-xl font-bold text-white mb-2">{activePath.name}</h3>
                                    
                                    <div className="mb-4">
                                        <div className="flex justify-between text-white/80 text-sm mb-1">
                                            <span>Progression</span>
                                            <span>{Math.round(activePath.completion_percentage)}%</span>
                                        </div>
                                        <div className="w-full bg-white/20 rounded-full h-2">
                                            <div 
                                                className="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-300"
                                                style={{ width: `${activePath.completion_percentage}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                    
                                    <button
                                        onClick={() => continuePath(fullPath)}
                                        className="w-full bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-3 px-4 rounded-xl transition-all duration-200"
                                    >
                                        Continuer
                                    </button>
                                </div>
                            );
                        })}
                    </div>
                </div>
            )}

            {/* Tous les parcours */}
            <div className="max-w-7xl mx-auto">
                <h2 className="text-2xl font-bold text-white mb-6">
                    {paths.length > 0 ? `${paths.length} parcours disponibles` : 'Aucun parcours trouvé'}
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {paths.map((path) => (
                        <div 
                            key={path.id} 
                            className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
                            style={{ borderLeft: `4px solid ${path.color}` }}
                        >
                            <div className="flex items-center justify-between mb-4">
                                <span className="text-3xl">{path.icon}</span>
                                <div className="flex space-x-2">
                                    <span className={`${getLevelColor(path.level)} text-white px-3 py-1 rounded-full text-xs font-bold`}>
                                        {path.level}
                                    </span>
                                    {path.is_completed && (
                                        <span className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                                            ✓ Terminé
                                        </span>
                                    )}
                                </div>
                            </div>
                            
                            <h3 className="text-xl font-bold text-white mb-2">{path.name}</h3>
                            <p className="text-white/80 text-sm mb-4 line-clamp-3">{path.description}</p>
                            
                            <div className="space-y-2 mb-4">
                                <div className="flex items-center justify-between text-white/70 text-sm">
                                    <span className="flex items-center space-x-1">
                                        <span>{getCategoryIcon(path.category)}</span>
                                        <span>{path.category}</span>
                                    </span>
                                    <span>{path.estimated_duration_hours}h</span>
                                </div>
                                
                                <div className="flex items-center justify-between text-white/70 text-sm">
                                    <span>{path.total_steps} étapes</span>
                                    <span>{path.completion_reward_xp} XP</span>
                                </div>
                                
                                {path.is_started && (
                                    <div className="mt-2">
                                        <div className="flex justify-between text-white/80 text-sm mb-1">
                                            <span>Progression</span>
                                            <span>{Math.round(path.completion_percentage)}%</span>
                                        </div>
                                        <div className="w-full bg-white/20 rounded-full h-2">
                                            <div 
                                                className="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-300"
                                                style={{ width: `${path.completion_percentage}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                )}
                            </div>
                            
                            <div className="space-y-2">
                                {!path.is_started ? (
                                    <button
                                        onClick={() => startPath(path.id)}
                                        className="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-xl transition-all duration-200"
                                    >
                                        Commencer
                                    </button>
                                ) : path.is_completed ? (
                                    <button
                                        onClick={() => navigate(`/learning-paths/${path.id}`)}
                                        className="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-4 rounded-xl transition-all duration-200"
                                    >
                                        Revoir
                                    </button>
                                ) : (
                                    <button
                                        onClick={() => continuePath(path)}
                                        className="w-full bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-3 px-4 rounded-xl transition-all duration-200"
                                    >
                                        Continuer
                                    </button>
                                )}
                                
                                <button
                                    onClick={() => navigate(`/learning-paths/${path.id}`)}
                                    className="w-full bg-white/20 hover:bg-white/30 text-white font-bold py-2 px-4 rounded-xl transition-all duration-200"
                                >
                                    Détails
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default LearningPaths;
