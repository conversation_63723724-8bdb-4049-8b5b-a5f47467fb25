import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { exerciseService } from '../../services/exerciseApi';

function Exercises() {
    const [exercises, setExercises] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [filters, setFilters] = useState({
        language: '',
        level: '',
        type: ''
    });

    const languages = ['Anglais', 'Espagnol', 'Italien', 'Français', 'Allemand'];
    const levels = ['Débutant', 'Intermédiaire', 'Avancé'];
    const types = ['Vocabulaire', 'Grammaire', 'Écoute', 'Lecture', 'Expression'];

    // Charger les exercices
    useEffect(() => {
        loadExercises();
    }, [filters]);

    const loadExercises = async () => {
        try {
            setLoading(true);
            const response = await exerciseService.getExercises(filters);
            if (response.success) {
                setExercises(response.data);
            }
        } catch (err) {
            setError('Erreur lors du chargement des exercices');
            console.error('Erreur:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleFilterChange = (filterName, value) => {
        setFilters(prev => ({
            ...prev,
            [filterName]: value
        }));
    };

    const resetFilters = () => {
        setFilters({
            language: '',
            level: '',
            type: ''
        });
    };

    const getTypeIcon = (type) => {
        switch (type) {
            case 'Vocabulaire':
                return '📚';
            case 'Grammaire':
                return '📝';
            case 'Écoute':
                return '🎧';
            case 'Lecture':
                return '📖';
            case 'Expression':
                return '💬';
            default:
                return '🎯';
        }
    };

    const getDifficultyColor = (difficulty) => {
        switch (difficulty) {
            case 1:
                return 'bg-green-100 text-green-800';
            case 2:
                return 'bg-yellow-100 text-yellow-800';
            case 3:
                return 'bg-orange-100 text-orange-800';
            case 4:
                return 'bg-red-100 text-red-800';
            case 5:
                return 'bg-purple-100 text-purple-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* En-tête */}
                <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold text-gray-800 mb-4">
                        Exercices de Langues
                    </h1>
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                        Pratiquez et améliorez vos compétences linguistiques avec nos exercices interactifs gratuits
                    </p>
                </div>

                {/* Filtres */}
                <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                    <h2 className="text-xl font-semibold mb-4">Filtrer les exercices</h2>
                    
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Langue
                            </label>
                            <select
                                value={filters.language}
                                onChange={(e) => handleFilterChange('language', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="">Toutes les langues</option>
                                {languages.map(language => (
                                    <option key={language} value={language}>{language}</option>
                                ))}
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Niveau
                            </label>
                            <select
                                value={filters.level}
                                onChange={(e) => handleFilterChange('level', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="">Tous les niveaux</option>
                                {levels.map(level => (
                                    <option key={level} value={level}>{level}</option>
                                ))}
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Type
                            </label>
                            <select
                                value={filters.type}
                                onChange={(e) => handleFilterChange('type', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="">Tous les types</option>
                                {types.map(type => (
                                    <option key={type} value={type}>{type}</option>
                                ))}
                            </select>
                        </div>

                        <div className="flex items-end">
                            <button
                                onClick={resetFilters}
                                className="w-full bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors"
                            >
                                Réinitialiser
                            </button>
                        </div>
                    </div>
                </div>

                {/* Liste des exercices */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h2 className="text-2xl font-bold mb-6">Exercices disponibles</h2>

                    {loading && (
                        <div className="text-center text-gray-500 py-8">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                            <p>Chargement des exercices...</p>
                        </div>
                    )}

                    {error && (
                        <div className="text-center text-red-500 py-8">
                            <p>{error}</p>
                            <button
                                onClick={loadExercises}
                                className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                            >
                                Réessayer
                            </button>
                        </div>
                    )}

                    {!loading && !error && exercises.length === 0 && (
                        <div className="text-center text-gray-500 py-8">
                            <p>Aucun exercice trouvé avec ces critères.</p>
                            <button
                                onClick={resetFilters}
                                className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                            >
                                Voir tous les exercices
                            </button>
                        </div>
                    )}

                    {!loading && !error && exercises.length > 0 && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {exercises.map(exercise => (
                                <div key={exercise.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                                    <div className="flex items-start justify-between mb-4">
                                        <div className="flex items-center space-x-2">
                                            <span className="text-2xl">{getTypeIcon(exercise.type)}</span>
                                            <div>
                                                <h3 className="font-semibold text-gray-800">{exercise.title}</h3>
                                                <p className="text-sm text-gray-600">{exercise.language} - {exercise.level}</p>
                                            </div>
                                        </div>
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exercise.difficulty)}`}>
                                            Niveau {exercise.difficulty}
                                        </span>
                                    </div>

                                    <p className="text-gray-600 text-sm mb-4">
                                        {exercise.description}
                                    </p>

                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                                            <span>{exercise.type}</span>
                                            <span>•</span>
                                            <span>{exercise.points} points</span>
                                        </div>
                                        <Link
                                            to={`/exercise/${exercise.id}`}
                                            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
                                        >
                                            Commencer
                                        </Link>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

export default Exercises;
