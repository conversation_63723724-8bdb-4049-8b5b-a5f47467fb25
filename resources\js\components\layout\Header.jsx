import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

function Header() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const location = useLocation();

    const isActive = (path) => location.pathname === path;

    return (
        <header className="bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg sticky top-0 z-40">
            <div className="container mx-auto px-4">
                <div className="flex justify-between items-center py-4">
                    {/* Logo */}
                    <Link to="/" className="text-2xl font-bold flex items-center space-x-2 hover:scale-105 transition-transform">
                        <span className="text-3xl">🎓</span>
                        <span>Learnify</span>
                    </Link>

                    {/* Desktop Navigation */}
                    <nav className="hidden md:flex space-x-1">
                        <Link
                            to="/"
                            className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                                isActive('/')
                                    ? 'bg-white bg-opacity-20 text-white font-semibold'
                                    : 'hover:bg-white hover:bg-opacity-10'
                            }`}
                        >
                            Accueil
                        </Link>
                        <Link
                            to="/about"
                            className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                                isActive('/about')
                                    ? 'bg-white bg-opacity-20 text-white font-semibold'
                                    : 'hover:bg-white hover:bg-opacity-10'
                            }`}
                        >
                            À propos
                        </Link>
                        <Link
                            to="/admin"
                            className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                                isActive('/admin')
                                    ? 'bg-white bg-opacity-20 text-white font-semibold'
                                    : 'hover:bg-white hover:bg-opacity-10'
                            }`}
                        >
                            <span className="flex items-center space-x-1">
                                <span>⚙️</span>
                                <span>Admin</span>
                            </span>
                        </Link>
                    </nav>

                    {/* Mobile menu button */}
                    <button
                        className="md:hidden p-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors"
                        onClick={() => setIsMenuOpen(!isMenuOpen)}
                    >
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            {isMenuOpen ? (
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            ) : (
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                            )}
                        </svg>
                    </button>
                </div>

                {/* Mobile Navigation */}
                {isMenuOpen && (
                    <nav className="md:hidden pb-4 border-t border-white border-opacity-20">
                        <div className="flex flex-col space-y-1 pt-4">
                            <Link
                                to="/"
                                className={`px-4 py-3 rounded-lg transition-all duration-200 ${
                                    isActive('/')
                                        ? 'bg-white bg-opacity-20 text-white font-semibold'
                                        : 'hover:bg-white hover:bg-opacity-10'
                                }`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                🏠 Accueil
                            </Link>
                            <Link
                                to="/about"
                                className={`px-4 py-3 rounded-lg transition-all duration-200 ${
                                    isActive('/about')
                                        ? 'bg-white bg-opacity-20 text-white font-semibold'
                                        : 'hover:bg-white hover:bg-opacity-10'
                                }`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                ℹ️ À propos
                            </Link>
                            <Link
                                to="/admin"
                                className={`px-4 py-3 rounded-lg transition-all duration-200 ${
                                    isActive('/admin')
                                        ? 'bg-white bg-opacity-20 text-white font-semibold'
                                        : 'hover:bg-white hover:bg-opacity-10'
                                }`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                ⚙️ Administration
                            </Link>
                        </div>
                    </nav>
                )}
            </div>
        </header>
    );
}

export default Header;
