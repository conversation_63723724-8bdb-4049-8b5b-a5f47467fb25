import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

function Header() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const location = useLocation();

    const isActive = (path) => location.pathname === path;

    return (
        <header className="bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg sticky top-0 z-40">
            <div className="container mx-auto px-4">
                <div className="flex justify-between items-center py-4">
                    {/* Logo */}
                    <Link to="/" className="text-2xl font-bold flex items-center space-x-2 hover:scale-105 transition-transform">
                        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"/>
                        </svg>
                        <span>Learnify</span>
                    </Link>

                    {/* Desktop Navigation */}
                    <nav className="hidden md:flex space-x-1">
                        <Link
                            to="/"
                            className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                                isActive('/')
                                    ? 'bg-white bg-opacity-20 text-white font-semibold'
                                    : 'hover:bg-white hover:bg-opacity-10'
                            }`}
                        >
                            Accueil
                        </Link>
                        <Link
                            to="/exercises"
                            className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                                isActive('/exercises') || location.pathname.startsWith('/exercise')
                                    ? 'bg-white bg-opacity-20 text-white font-semibold'
                                    : 'hover:bg-white hover:bg-opacity-10'
                            }`}
                        >
                            Exercices
                        </Link>
                        <Link
                            to="/about"
                            className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                                isActive('/about')
                                    ? 'bg-white bg-opacity-20 text-white font-semibold'
                                    : 'hover:bg-white hover:bg-opacity-10'
                            }`}
                        >
                            À propos
                        </Link>
                        <Link
                            to="/admin"
                            className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                                isActive('/admin')
                                    ? 'bg-white bg-opacity-20 text-white font-semibold'
                                    : 'hover:bg-white hover:bg-opacity-10'
                            }`}
                        >
                            <span className="flex items-center space-x-1">
                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                                </svg>
                                <span>Admin</span>
                            </span>
                        </Link>
                    </nav>

                    {/* Mobile menu button */}
                    <button
                        className="md:hidden p-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors"
                        onClick={() => setIsMenuOpen(!isMenuOpen)}
                    >
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            {isMenuOpen ? (
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            ) : (
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                            )}
                        </svg>
                    </button>
                </div>

                {/* Mobile Navigation */}
                {isMenuOpen && (
                    <nav className="md:hidden pb-4 border-t border-white border-opacity-20">
                        <div className="flex flex-col space-y-1 pt-4">
                            <Link
                                to="/"
                                className={`px-4 py-3 rounded-lg transition-all duration-200 ${
                                    isActive('/')
                                        ? 'bg-white bg-opacity-20 text-white font-semibold'
                                        : 'hover:bg-white hover:bg-opacity-10'
                                }`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                <span className="flex items-center space-x-2">
                                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                                    </svg>
                                    <span>Accueil</span>
                                </span>
                            </Link>
                            <Link
                                to="/exercises"
                                className={`px-4 py-3 rounded-lg transition-all duration-200 ${
                                    isActive('/exercises') || location.pathname.startsWith('/exercise')
                                        ? 'bg-white bg-opacity-20 text-white font-semibold'
                                        : 'hover:bg-white hover:bg-opacity-10'
                                }`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                <span className="flex items-center space-x-2">
                                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span>Exercices</span>
                                </span>
                            </Link>
                            <Link
                                to="/about"
                                className={`px-4 py-3 rounded-lg transition-all duration-200 ${
                                    isActive('/about')
                                        ? 'bg-white bg-opacity-20 text-white font-semibold'
                                        : 'hover:bg-white hover:bg-opacity-10'
                                }`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                <span className="flex items-center space-x-2">
                                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
                                    </svg>
                                    <span>À propos</span>
                                </span>
                            </Link>
                            <Link
                                to="/admin"
                                className={`px-4 py-3 rounded-lg transition-all duration-200 ${
                                    isActive('/admin')
                                        ? 'bg-white bg-opacity-20 text-white font-semibold'
                                        : 'hover:bg-white hover:bg-opacity-10'
                                }`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                <span className="flex items-center space-x-2">
                                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                                    </svg>
                                    <span>Administration</span>
                                </span>
                            </Link>
                        </div>
                    </nav>
                )}
            </div>
        </header>
    );
}

export default Header;
