import React, { useState } from 'react';
import { Link } from 'react-router-dom';

function Header() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);

    return (
        <header className="bg-blue-600 text-white shadow-lg">
            <div className="container mx-auto px-4">
                <div className="flex justify-between items-center py-4">
                    {/* Logo */}
                    <Link to="/" className="text-2xl font-bold">
                        📚 Learnify
                    </Link>

                    {/* Desktop Navigation */}
                    <nav className="hidden md:flex space-x-6">
                        <Link to="/" className="hover:text-blue-200 transition-colors">
                            Accueil
                        </Link>
                        <Link to="/about" className="hover:text-blue-200 transition-colors">
                            À propos
                        </Link>
                        <Link to="/admin" className="hover:text-blue-200 transition-colors">
                            Administration
                        </Link>
                    </nav>

                    {/* Mobile menu button */}
                    <button
                        className="md:hidden"
                        onClick={() => setIsMenuOpen(!isMenuOpen)}
                    >
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>

                {/* Mobile Navigation */}
                {isMenuOpen && (
                    <nav className="md:hidden pb-4">
                        <div className="flex flex-col space-y-2">
                            <Link to="/" className="hover:text-blue-200 transition-colors py-2">
                                Accueil
                            </Link>
                            <Link to="/about" className="hover:text-blue-200 transition-colors py-2">
                                À propos
                            </Link>
                            <Link to="/admin" className="hover:text-blue-200 transition-colors py-2">
                                Administration
                            </Link>
                        </div>
                    </nav>
                )}
            </div>
        </header>
    );
}

export default Header;
