import axios from 'axios';

// Configuration de base pour les requêtes API
const API_BASE_URL = '/api';

// Données de fallback pour les exercices
const fallbackExercises = [
    {
        id: 1,
        language: 'Anglais',
        level: 'Débutant',
        type: 'Vocabulaire',
        title: 'Les salutations en anglais',
        description: 'Apprenez les salutations de base en anglais',
        difficulty: 1,
        points: 15,
        content: {
            questions: [
                {
                    question: 'Comment dit-on "Bonjour" en anglais ?',
                    options: ['Hello', 'Goodbye', 'Please', 'Thank you'],
                    correct: 0
                },
                {
                    question: 'Comment dit-on "Au revoir" en anglais ?',
                    options: ['Hello', 'Goodbye', 'Good morning', 'Good night'],
                    correct: 1
                }
            ]
        }
    },
    {
        id: 2,
        language: 'Espagnol',
        level: 'Débutant',
        type: 'Vocabulaire',
        title: 'Los colores en español',
        description: 'Apprenez les couleurs en espagnol',
        difficulty: 1,
        points: 15,
        content: {
            questions: [
                {
                    question: 'Comment dit-on "rouge" en espagnol ?',
                    options: ['azul', 'rojo', 'verde', 'amarillo'],
                    correct: 1
                },
                {
                    question: 'Comment dit-on "bleu" en espagnol ?',
                    options: ['azul', 'rojo', 'verde', 'amarillo'],
                    correct: 0
                }
            ]
        }
    }
];

// Service pour les exercices
export const exerciseService = {
    // Récupérer tous les exercices avec filtres optionnels
    async getExercises(filters = {}) {
        try {
            const params = new URLSearchParams();
            
            if (filters.language) params.append('language', filters.language);
            if (filters.level) params.append('level', filters.level);
            if (filters.type) params.append('type', filters.type);

            const response = await axios.get(`${API_BASE_URL}/exercises?${params}`);
            
            if (response.data && response.data.success) {
                return {
                    success: true,
                    data: response.data.data
                };
            }
            
            throw new Error('Format de réponse invalide');
            
        } catch (error) {
            console.warn('Erreur API exercices, utilisation des données de fallback:', error);
            
            // Appliquer les filtres aux données de fallback
            let filteredExercises = [...fallbackExercises];
            
            if (filters.language) {
                filteredExercises = filteredExercises.filter(ex => ex.language === filters.language);
            }
            if (filters.level) {
                filteredExercises = filteredExercises.filter(ex => ex.level === filters.level);
            }
            if (filters.type) {
                filteredExercises = filteredExercises.filter(ex => ex.type === filters.type);
            }
            
            return {
                success: true,
                data: filteredExercises
            };
        }
    },

    // Récupérer un exercice spécifique
    async getExercise(id) {
        try {
            const response = await axios.get(`${API_BASE_URL}/exercises/${id}`);
            
            if (response.data && response.data.success) {
                return {
                    success: true,
                    data: response.data.data
                };
            }
            
            throw new Error('Format de réponse invalide');
            
        } catch (error) {
            console.warn('Erreur API exercice, utilisation des données de fallback:', error);
            
            const exercise = fallbackExercises.find(ex => ex.id == id);
            if (exercise) {
                return {
                    success: true,
                    data: exercise
                };
            }
            
            return {
                success: false,
                error: 'Exercice non trouvé'
            };
        }
    },

    // Soumettre les réponses d'un exercice
    async submitAnswers(exerciseId, answers, userId = null) {
        try {
            const response = await axios.post(`${API_BASE_URL}/exercises/${exerciseId}/submit`, {
                answers,
                user_id: userId
            });
            
            if (response.data && response.data.success) {
                return {
                    success: true,
                    data: response.data.data
                };
            }
            
            throw new Error('Format de réponse invalide');
            
        } catch (error) {
            console.warn('Erreur API soumission, calcul local:', error);
            
            // Calcul local du score pour le fallback
            const exercise = fallbackExercises.find(ex => ex.id == exerciseId);
            if (exercise) {
                let score = 0;
                const maxScore = exercise.content.questions.length;
                
                exercise.content.questions.forEach((question, index) => {
                    if (answers[index] === question.correct) {
                        score++;
                    }
                });
                
                return {
                    success: true,
                    data: {
                        score,
                        max_score: maxScore,
                        percentage: Math.round((score / maxScore) * 100),
                        points_earned: Math.round((score / maxScore) * exercise.points)
                    }
                };
            }
            
            return {
                success: false,
                error: 'Erreur lors de la soumission'
            };
        }
    },

    // Récupérer les exercices par langue
    async getExercisesByLanguage(language) {
        try {
            const response = await axios.get(`${API_BASE_URL}/exercises/language/${language}`);
            
            if (response.data && response.data.success) {
                return {
                    success: true,
                    data: response.data.data
                };
            }
            
            throw new Error('Format de réponse invalide');
            
        } catch (error) {
            console.warn('Erreur API exercices par langue, utilisation des données de fallback:', error);
            
            const exercisesByLanguage = fallbackExercises.filter(ex => ex.language === language);
            
            return {
                success: true,
                data: exercisesByLanguage
            };
        }
    }
};
