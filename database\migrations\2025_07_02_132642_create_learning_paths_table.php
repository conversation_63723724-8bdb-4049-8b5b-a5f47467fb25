<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('learning_paths', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('language');
            $table->text('description');
            $table->string('level'); // Débutant, Intermédiaire, Avancé
            $table->string('category'); // Vocabulaire, Grammaire, Conversation, etc.
            $table->json('lesson_sequence'); // Ordre des leçons
            $table->json('exercise_sequence'); // Ordre des exercices
            $table->integer('estimated_duration_hours')->default(0);
            $table->string('difficulty_progression')->default('linear'); // linear, adaptive
            $table->json('prerequisites')->nullable(); // Prérequis
            $table->json('learning_objectives')->nullable(); // Objectifs d'apprentissage
            $table->string('icon')->default('📚');
            $table->string('color')->default('#6366f1');
            $table->boolean('is_active')->default(true);
            $table->integer('completion_reward_xp')->default(100);
            $table->timestamps();

            $table->index(['language', 'level']);
            $table->index(['category']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('learning_paths');
    }
};
