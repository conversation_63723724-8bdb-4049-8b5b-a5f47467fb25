import React from 'react';
import { Routes, Route } from 'react-router-dom';
import Header from './layout/Header';
import Footer from './layout/Footer';
import Home from './pages/Home';
import CourseDetails from './pages/CourseDetails';
import About from './pages/About';
import Admin from './pages/Admin';
import Exercises from './pages/Exercises';
import ExerciseDetail from './pages/ExerciseDetail';
import Learn from './pages/Learn';
import LanguageDashboard from './pages/LanguageDashboard';
import Lessons from './pages/Lessons';

function App() {
    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex flex-col">
            <Header />
            <main className="flex-grow">
                <Routes>
                    <Route path="/" element={<Learn />} />
                    <Route path="/home" element={<Home />} />
                    <Route path="/learn" element={<Learn />} />
                    <Route path="/learn/:language" element={<LanguageDashboard />} />
                    <Route path="/learn/:language/lessons" element={<Lessons />} />
                    <Route path="/learn/:language/exercises" element={<div className="p-8 text-center"><h1 className="text-4xl">Exercices en développement...</h1></div>} />
                    <Route path="/learn/:language/review" element={<div className="p-8 text-center"><h1 className="text-4xl">Révision en développement...</h1></div>} />
                    <Route path="/course/:id" element={<CourseDetails />} />
                    <Route path="/about" element={<About />} />
                    <Route path="/admin" element={<Admin />} />
                    <Route path="/exercises" element={<Exercises />} />
                    <Route path="/exercise/:id" element={<ExerciseDetail />} />
                </Routes>
            </main>
            <Footer />
        </div>
    );
}

export default App;
