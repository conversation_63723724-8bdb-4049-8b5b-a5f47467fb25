<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Course extends Model
{
    protected $fillable = [
        'language',
        'level',
        'title',
        'description',
        'duration_weeks',
        'available_spots',
        'max_spots',
        'start_date',
        'format',
        'price',
        'is_active'
    ];

    protected $casts = [
        'start_date' => 'date',
        'price' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    /**
     * Relation avec les inscriptions
     */
    public function registrations(): HasMany
    {
        return $this->hasMany(Registration::class);
    }

    /**
     * Inscriptions confirmées
     */
    public function confirmedRegistrations(): HasMany
    {
        return $this->registrations()->where('status', 'Confirmée');
    }

    /**
     * Nombre de places restantes
     */
    public function getRemainingSpots(): int
    {
        return $this->available_spots - $this->confirmedRegistrations()->count();
    }

    /**
     * Vérifier si le cours est complet
     */
    public function isFull(): bool
    {
        return $this->getRemainingSpots() <= 0;
    }

    /**
     * Scope pour les cours actifs
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope pour filtrer par langue
     */
    public function scopeByLanguage($query, $language)
    {
        return $query->where('language', $language);
    }

    /**
     * Scope pour filtrer par niveau
     */
    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }
}
