<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class UserProgress extends Model
{
    protected $fillable = [
        'user_id',
        'language',
        'current_level',
        'total_xp',
        'daily_xp',
        'streak_days',
        'last_activity_date',
        'completed_lessons',
        'achievements'
    ];

    protected $casts = [
        'last_activity_date' => 'date',
        'completed_lessons' => 'array',
        'achievements' => 'array'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function addXp($amount)
    {
        $this->total_xp += $amount;
        $this->daily_xp += $amount;
        $this->updateStreak();
        $this->checkLevelUp();
        $this->save();
    }

    public function completeLesson($lessonId)
    {
        $completed = $this->completed_lessons ?? [];
        if (!in_array($lessonId, $completed)) {
            $completed[] = $lessonId;
            $this->completed_lessons = $completed;
            $this->save();
        }
    }

    private function updateStreak()
    {
        $today = Carbon::today();
        $lastActivity = $this->last_activity_date;

        if (!$lastActivity || $lastActivity->lt($today->subDay())) {
            // Streak cassé ou premier jour
            $this->streak_days = 1;
        } elseif ($lastActivity->eq($today->subDay())) {
            // Activité hier, continuer le streak
            $this->streak_days += 1;
        }
        // Si activité aujourd'hui, ne pas changer le streak

        $this->last_activity_date = $today;
    }

    private function checkLevelUp()
    {
        $xpForNextLevel = $this->current_level * 100; // 100 XP par niveau
        if ($this->total_xp >= $xpForNextLevel) {
            $this->current_level += 1;
            $this->addAchievement("level_{$this->current_level}");
        }
    }

    public function addAchievement($achievement)
    {
        $achievements = $this->achievements ?? [];
        if (!in_array($achievement, $achievements)) {
            $achievements[] = $achievement;
            $this->achievements = $achievements;
        }
    }
}
