<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->id();
            $table->string('language'); // Anglais, Espagnol, Italien, etc.
            $table->enum('level', ['Débutant', 'Intermédiaire', 'Avancé']);
            $table->string('title'); // Titre du cours
            $table->text('description'); // Description détaillée
            $table->integer('duration_weeks'); // Durée en semaines
            $table->integer('available_spots'); // Places disponibles
            $table->integer('max_spots'); // Nombre maximum de places
            $table->date('start_date'); // Date de début
            $table->enum('format', ['Visioconférence', 'Présentiel', 'Hybride']);
            $table->decimal('price', 8, 2)->nullable(); // Prix (nullable car pas de paiement pour l'instant)
            $table->boolean('is_active')->default(true); // Cours actif ou non
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('courses');
    }
};
