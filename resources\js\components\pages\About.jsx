import React from 'react';

function About() {
    return (
        <div className="container mx-auto px-4 py-8">
            <div className="max-w-4xl mx-auto">
                <h1 className="text-4xl font-bold text-gray-800 mb-8 text-center">
                    À propos de Learnify
                </h1>

                <div className="bg-white rounded-lg shadow-md p-8 mb-8">
                    <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                        Notre mission
                    </h2>
                    <p className="text-gray-600 text-lg leading-relaxed mb-6">
                        Learnify est une plateforme dédiée à la réservation de cours de langues. 
                        Elle vise à faciliter l'accès à l'apprentissage linguistique pour tous, 
                        à travers une interface claire, intuitive et multilingue.
                    </p>
                    
                    <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                        Pourquoi choisir Learnify ?
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="flex items-start space-x-3">
                            <div className="text-2xl">✨</div>
                            <div>
                                <h3 className="font-semibold text-gray-800">Interface intuitive</h3>
                                <p className="text-gray-600">Une plateforme simple et facile à utiliser pour tous.</p>
                            </div>
                        </div>
                        <div className="flex items-start space-x-3">
                            <div className="text-2xl">🌍</div>
                            <div>
                                <h3 className="font-semibold text-gray-800">Multilingue</h3>
                                <p className="text-gray-600">Apprenez différentes langues selon vos besoins.</p>
                            </div>
                        </div>
                        <div className="flex items-start space-x-3">
                            <div className="text-2xl">📅</div>
                            <div>
                                <h3 className="font-semibold text-gray-800">Réservation simple</h3>
                                <p className="text-gray-600">Réservez vos cours en quelques clics seulement.</p>
                            </div>
                        </div>
                        <div className="flex items-start space-x-3">
                            <div className="text-2xl">🎓</div>
                            <div>
                                <h3 className="font-semibold text-gray-800">Tous niveaux</h3>
                                <p className="text-gray-600">De débutant à avancé, nous avons des cours pour tous.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg p-8 text-center">
                    <h2 className="text-2xl font-bold mb-4">
                        Prêt à commencer votre apprentissage ?
                    </h2>
                    <p className="text-lg mb-6">
                        Découvrez nos cours disponibles et réservez votre place dès maintenant.
                    </p>
                    <a 
                        href="/" 
                        className="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                    >
                        Voir les cours
                    </a>
                </div>
            </div>
        </div>
    );
}

export default About;
