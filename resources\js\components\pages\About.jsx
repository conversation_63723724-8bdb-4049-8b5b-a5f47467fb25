import React from 'react';
import { Link } from 'react-router-dom';

function About() {
    return (
        <div className="container mx-auto px-4 py-8">
            <div className="max-w-4xl mx-auto">
                <h1 className="text-4xl font-bold text-gray-800 mb-8 text-center">
                    À propos de Learnify
                </h1>

                <div className="bg-white rounded-lg shadow-md p-8 mb-8">
                    <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                        Notre mission
                    </h2>
                    <p className="text-gray-600 text-lg leading-relaxed mb-6">
                        Learnify est une plateforme dédiée à la réservation de cours de langues en ligne.
                        Notre mission est de rendre l'apprentissage des langues accessible à tous,
                        en proposant des cours de qualité avec des professeurs expérimentés,
                        dans un environnement d'apprentissage moderne et flexible.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div className="text-center">
                            <div className="text-4xl mb-3">🎯</div>
                            <h3 className="font-semibold text-gray-800 mb-2">Excellence pédagogique</h3>
                            <p className="text-gray-600 text-sm">Des méthodes d'enseignement éprouvées et des contenus adaptés à chaque niveau.</p>
                        </div>
                        <div className="text-center">
                            <div className="text-4xl mb-3">🤝</div>
                            <h3 className="font-semibold text-gray-800 mb-2">Accompagnement personnalisé</h3>
                            <p className="text-gray-600 text-sm">Un suivi individuel pour maximiser vos progrès et atteindre vos objectifs.</p>
                        </div>
                        <div className="text-center">
                            <div className="text-4xl mb-3">🌐</div>
                            <h3 className="font-semibold text-gray-800 mb-2">Flexibilité totale</h3>
                            <p className="text-gray-600 text-sm">Cours en visioconférence, présentiel ou hybride selon vos préférences.</p>
                        </div>
                    </div>
                    
                    <h2 className="text-2xl font-semibold text-gray-800 mb-6">
                        Nos langues disponibles
                    </h2>
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <div className="text-2xl mb-2">🇬🇧</div>
                            <p className="font-medium text-gray-800">Anglais</p>
                        </div>
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <div className="text-2xl mb-2">🇪🇸</div>
                            <p className="font-medium text-gray-800">Espagnol</p>
                        </div>
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <div className="text-2xl mb-2">🇮🇹</div>
                            <p className="font-medium text-gray-800">Italien</p>
                        </div>
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <div className="text-2xl mb-2">🇫🇷</div>
                            <p className="font-medium text-gray-800">Français</p>
                        </div>
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <div className="text-2xl mb-2">🇩🇪</div>
                            <p className="font-medium text-gray-800">Allemand</p>
                        </div>
                    </div>

                    <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                        Pourquoi choisir Learnify ?
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="flex items-start space-x-3">
                            <div className="text-2xl">✨</div>
                            <div>
                                <h3 className="font-semibold text-gray-800">Interface intuitive</h3>
                                <p className="text-gray-600">Une plateforme simple et facile à utiliser pour réserver vos cours en quelques clics.</p>
                            </div>
                        </div>
                        <div className="flex items-start space-x-3">
                            <div className="text-2xl">👨‍🏫</div>
                            <div>
                                <h3 className="font-semibold text-gray-800">Professeurs qualifiés</h3>
                                <p className="text-gray-600">Des enseignants expérimentés et natifs pour un apprentissage authentique.</p>
                            </div>
                        </div>
                        <div className="flex items-start space-x-3">
                            <div className="text-2xl">📅</div>
                            <div>
                                <h3 className="font-semibold text-gray-800">Horaires flexibles</h3>
                                <p className="text-gray-600">Choisissez vos créneaux selon votre emploi du temps.</p>
                            </div>
                        </div>
                        <div className="flex items-start space-x-3">
                            <div className="text-2xl">🎓</div>
                            <div>
                                <h3 className="font-semibold text-gray-800">Tous niveaux</h3>
                                <p className="text-gray-600">De débutant à avancé, progressez à votre rythme.</p>
                            </div>
                        </div>
                        <div className="flex items-start space-x-3">
                            <div className="text-2xl">💻</div>
                            <div>
                                <h3 className="font-semibold text-gray-800">Formats variés</h3>
                                <p className="text-gray-600">Visioconférence, présentiel ou hybride selon vos préférences.</p>
                            </div>
                        </div>
                        <div className="flex items-start space-x-3">
                            <div className="text-2xl">📈</div>
                            <div>
                                <h3 className="font-semibold text-gray-800">Suivi personnalisé</h3>
                                <p className="text-gray-600">Un accompagnement individuel pour maximiser vos progrès.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg p-8 text-center">
                    <h2 className="text-2xl font-bold mb-4">
                        Prêt à commencer votre apprentissage ?
                    </h2>
                    <p className="text-lg mb-6">
                        Découvrez nos cours disponibles et réservez votre place dès maintenant.
                    </p>
                    <Link
                        to="/"
                        className="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                    >
                        Voir les cours
                    </Link>
                </div>

                {/* Section contact */}
                <div className="mt-8 bg-white rounded-lg shadow-md p-8">
                    <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
                        Contactez-nous
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                        <div>
                            <div className="text-3xl mb-3">📧</div>
                            <h3 className="font-semibold text-gray-800 mb-2">Email</h3>
                            <p className="text-gray-600"><EMAIL></p>
                        </div>
                        <div>
                            <div className="text-3xl mb-3">📞</div>
                            <h3 className="font-semibold text-gray-800 mb-2">Téléphone</h3>
                            <p className="text-gray-600">+33 1 23 45 67 89</p>
                        </div>
                        <div>
                            <div className="text-3xl mb-3">🕒</div>
                            <h3 className="font-semibold text-gray-800 mb-2">Horaires</h3>
                            <p className="text-gray-600">Lun-Ven: 9h-18h</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default About;
