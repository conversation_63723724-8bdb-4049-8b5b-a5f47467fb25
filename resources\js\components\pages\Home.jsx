import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { courseService, utilityService } from '../../services/api';
import CourseCard from '../ui/CourseCard';

function Home() {
    const [courses, setCourses] = useState([]);
    const [languages, setLanguages] = useState([]);
    const [levels, setLevels] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [filters, setFilters] = useState({
        language: '',
        level: '',
        sort_by: 'start_date',
        sort_order: 'asc'
    });

    // Charger les données initiales
    useEffect(() => {
        loadInitialData();
    }, []);

    // Charger les cours quand les filtres changent
    useEffect(() => {
        loadCourses();
    }, [filters]);

    const loadInitialData = async () => {
        try {
            const [languagesData, levelsData] = await Promise.all([
                utilityService.getLanguages(),
                utilityService.getLevels()
            ]);

            if (languagesData.success) setLanguages(languagesData.data);
            if (levelsData.success) setLevels(levelsData.data);
        } catch (err) {
            console.error('Erreur lors du chargement des données initiales:', err);
        }
    };

    const loadCourses = async () => {
        try {
            setLoading(true);
            const response = await courseService.getCourses(filters);
            if (response.success) {
                setCourses(response.data);
            }
        } catch (err) {
            setError('Erreur lors du chargement des cours');
            console.error('Erreur:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleFilterChange = (key, value) => {
        setFilters(prev => ({
            ...prev,
            [key]: value
        }));
    };

    const resetFilters = () => {
        setFilters({
            language: '',
            level: '',
            sort_by: 'start_date',
            sort_order: 'asc'
        });
    };
    return (
        <div className="container mx-auto px-4 py-8">
            <div className="text-center mb-12">
                <h1 className="text-4xl font-bold text-gray-800 mb-4">
                    Bienvenue sur Learnify
                </h1>
                <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                    Découvrez nos cours de langues et réservez votre place pour commencer 
                    votre apprentissage linguistique dès aujourd'hui.
                </p>
            </div>

            {/* Hero Section */}
            <div className="relative bg-gradient-to-r from-blue-500 via-purple-600 to-pink-500 text-white rounded-2xl p-12 mb-12 overflow-hidden animate-fadeInUp">
                {/* Éléments décoratifs animés */}
                <div className="absolute top-4 right-4 text-4xl animate-bounce-slow">🎓</div>
                <div className="absolute bottom-4 left-4 text-3xl floating">📚</div>
                <div className="absolute top-1/2 right-8 text-2xl animate-pulse-slow">✨</div>

                <div className="relative max-w-4xl mx-auto text-center">
                    <h2 className="text-4xl md:text-5xl font-bold mb-6 animate-slideInRight">
                        Apprenez une nouvelle langue
                        <span className="block text-2xl md:text-3xl mt-2 text-blue-100">
                            et ouvrez-vous au monde 🌍
                        </span>
                    </h2>
                    <p className="text-lg md:text-xl mb-8 text-blue-50 leading-relaxed animate-fadeInUp">
                        Choisissez parmi nos cours d'anglais, espagnol, italien, français et allemand.
                        Tous niveaux acceptés, de débutant à avancé. Rejoignez notre communauté
                        d'apprenants passionnés !
                    </p>

                    <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-fadeInUp">
                        <button
                            onClick={() => document.getElementById('courses-section')?.scrollIntoView({ behavior: 'smooth' })}
                            className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 hover-lift shadow-lg"
                        >
                            🚀 Voir les cours disponibles
                        </button>
                        <Link
                            to="/about"
                            className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300 transform hover:scale-105"
                        >
                            📖 En savoir plus
                        </Link>
                    </div>

                    {/* Statistiques */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 pt-8 border-t border-white border-opacity-20">
                        <div className="text-center animate-fadeInUp">
                            <div className="text-2xl md:text-3xl font-bold">500+</div>
                            <div className="text-sm text-blue-100">Étudiants</div>
                        </div>
                        <div className="text-center animate-fadeInUp">
                            <div className="text-2xl md:text-3xl font-bold">5</div>
                            <div className="text-sm text-blue-100">Langues</div>
                        </div>
                        <div className="text-center animate-fadeInUp">
                            <div className="text-2xl md:text-3xl font-bold">98%</div>
                            <div className="text-sm text-blue-100">Satisfaction</div>
                        </div>
                        <div className="text-center animate-fadeInUp">
                            <div className="text-2xl md:text-3xl font-bold">24/7</div>
                            <div className="text-sm text-blue-100">Support</div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <div className="text-center p-6 bg-white rounded-lg shadow-md">
                    <div className="text-4xl mb-4">🎯</div>
                    <h3 className="text-xl font-semibold mb-2">Cours ciblés</h3>
                    <p className="text-gray-600">
                        Des cours adaptés à votre niveau et vos objectifs d'apprentissage.
                    </p>
                </div>
                <div className="text-center p-6 bg-white rounded-lg shadow-md">
                    <div className="text-4xl mb-4">👥</div>
                    <h3 className="text-xl font-semibold mb-2">Petits groupes</h3>
                    <p className="text-gray-600">
                        Apprenez en petits groupes pour une attention personnalisée.
                    </p>
                </div>
                <div className="text-center p-6 bg-white rounded-lg shadow-md">
                    <div className="text-4xl mb-4">💻</div>
                    <h3 className="text-xl font-semibold mb-2">Format flexible</h3>
                    <p className="text-gray-600">
                        Cours en visioconférence ou en présentiel selon vos préférences.
                    </p>
                </div>
            </div>

            {/* Filtres */}
            <div id="courses-section" className="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 className="text-xl font-semibold mb-4">Filtrer les cours</h2>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Langue
                        </label>
                        <select
                            value={filters.language}
                            onChange={(e) => handleFilterChange('language', e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="">Toutes les langues</option>
                            {languages.map(language => (
                                <option key={language} value={language}>{language}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Niveau
                        </label>
                        <select
                            value={filters.level}
                            onChange={(e) => handleFilterChange('level', e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="">Tous les niveaux</option>
                            {levels.map(level => (
                                <option key={level} value={level}>{level}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Trier par
                        </label>
                        <select
                            value={filters.sort_by}
                            onChange={(e) => handleFilterChange('sort_by', e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="start_date">Date de début</option>
                            <option value="language">Langue</option>
                            <option value="level">Niveau</option>
                            <option value="price">Prix</option>
                        </select>
                    </div>
                    <div className="flex items-end">
                        <button
                            onClick={resetFilters}
                            className="w-full bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors"
                        >
                            Réinitialiser
                        </button>
                    </div>
                </div>
            </div>

            {/* Liste des cours */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-bold mb-6">Cours disponibles</h2>

                {loading && (
                    <div className="text-center text-gray-500 py-8">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <p>Chargement des cours...</p>
                    </div>
                )}

                {error && (
                    <div className="text-center text-red-500 py-8">
                        <p>{error}</p>
                        <button
                            onClick={loadCourses}
                            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                        >
                            Réessayer
                        </button>
                    </div>
                )}

                {!loading && !error && courses.length === 0 && (
                    <div className="text-center text-gray-500 py-8">
                        <p>Aucun cours trouvé avec ces critères.</p>
                        <button
                            onClick={resetFilters}
                            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                        >
                            Voir tous les cours
                        </button>
                    </div>
                )}

                {!loading && !error && courses.length > 0 && (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {courses.map(course => (
                            <CourseCard key={course.id} course={course} />
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}

export default Home;
