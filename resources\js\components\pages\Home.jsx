import React from 'react';

function Home() {
    return (
        <div className="container mx-auto px-4 py-8">
            <div className="text-center mb-12">
                <h1 className="text-4xl font-bold text-gray-800 mb-4">
                    Bienvenue sur Learnify
                </h1>
                <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                    Découvrez nos cours de langues et réservez votre place pour commencer 
                    votre apprentissage linguistique dès aujourd'hui.
                </p>
            </div>

            {/* Hero Section */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg p-8 mb-12">
                <div className="max-w-3xl mx-auto text-center">
                    <h2 className="text-3xl font-bold mb-4">
                        Apprenez une nouvelle langue
                    </h2>
                    <p className="text-lg mb-6">
                        Choisissez parmi nos cours d'anglais, espagnol, italien, français et allemand. 
                        Tous niveaux acceptés, de débutant à avancé.
                    </p>
                    <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        Voir les cours disponibles
                    </button>
                </div>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <div className="text-center p-6 bg-white rounded-lg shadow-md">
                    <div className="text-4xl mb-4">🎯</div>
                    <h3 className="text-xl font-semibold mb-2">Cours ciblés</h3>
                    <p className="text-gray-600">
                        Des cours adaptés à votre niveau et vos objectifs d'apprentissage.
                    </p>
                </div>
                <div className="text-center p-6 bg-white rounded-lg shadow-md">
                    <div className="text-4xl mb-4">👥</div>
                    <h3 className="text-xl font-semibold mb-2">Petits groupes</h3>
                    <p className="text-gray-600">
                        Apprenez en petits groupes pour une attention personnalisée.
                    </p>
                </div>
                <div className="text-center p-6 bg-white rounded-lg shadow-md">
                    <div className="text-4xl mb-4">💻</div>
                    <h3 className="text-xl font-semibold mb-2">Format flexible</h3>
                    <p className="text-gray-600">
                        Cours en visioconférence ou en présentiel selon vos préférences.
                    </p>
                </div>
            </div>

            {/* Temporary course list placeholder */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-bold mb-6">Cours disponibles</h2>
                <div className="text-center text-gray-500 py-8">
                    <p>Chargement des cours...</p>
                    <p className="text-sm mt-2">Les cours seront affichés ici une fois la base de données configurée.</p>
                </div>
            </div>
        </div>
    );
}

export default Home;
