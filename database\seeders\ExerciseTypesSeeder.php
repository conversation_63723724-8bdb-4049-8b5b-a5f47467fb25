<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ExerciseType;

class ExerciseTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $exerciseTypes = [
            [
                'name' => 'multiple_choice',
                'display_name' => 'Choix multiple',
                'description' => 'Choisissez la bonne réponse parmi plusieurs options',
                'config' => [
                    'min_options' => 3,
                    'max_options' => 4,
                    'show_images' => true
                ],
                'is_active' => true
            ],
            [
                'name' => 'translation',
                'display_name' => 'Traduction',
                'description' => 'Traduisez le mot ou la phrase',
                'config' => [
                    'allow_hints' => true,
                    'case_sensitive' => false
                ],
                'is_active' => true
            ],
            [
                'name' => 'listening',
                'display_name' => 'Écoute',
                'description' => 'Écoutez et écrivez ce que vous entendez',
                'config' => [
                    'playback_speed' => ['normal', 'slow'],
                    'max_attempts' => 3
                ],
                'is_active' => true
            ],
            [
                'name' => 'matching',
                'display_name' => 'Association',
                'description' => 'Associez les mots avec leur traduction',
                'config' => [
                    'min_pairs' => 4,
                    'max_pairs' => 6
                ],
                'is_active' => true
            ],
            [
                'name' => 'fill_blank',
                'display_name' => 'Compléter',
                'description' => 'Complétez la phrase avec le mot manquant',
                'config' => [
                    'show_word_bank' => true,
                    'multiple_blanks' => false
                ],
                'is_active' => true
            ],
            [
                'name' => 'pronunciation',
                'display_name' => 'Prononciation',
                'description' => 'Prononcez le mot correctement',
                'config' => [
                    'accuracy_threshold' => 0.7,
                    'max_attempts' => 3
                ],
                'is_active' => true
            ]
        ];

        foreach ($exerciseTypes as $type) {
            ExerciseType::create($type);
        }
    }
}
