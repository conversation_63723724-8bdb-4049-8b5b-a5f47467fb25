import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

const Lessons = () => {
    const { language } = useParams();
    const navigate = useNavigate();
    const [lessons, setLessons] = useState({});
    const [progress, setProgress] = useState(null);
    const [loading, setLoading] = useState(true);
    const [selectedLesson, setSelectedLesson] = useState(null);

    const languageNames = {
        'en': 'Anglais',
        'es': 'Espagnol', 
        'fr': 'Français',
        'de': 'Allemand',
        'it': 'Italien',
        'pt': 'Portugais',
        'zh': 'Chinois',
        'ja': 'Japonais'
    };

    const languageFlags = {
        'en': '🇬🇧',
        'es': '🇪🇸',
        'fr': '🇫🇷', 
        'de': '🇩🇪',
        'it': '🇮🇹',
        'pt': '🇵🇹',
        'zh': '🇨🇳',
        'ja': '🇯🇵'
    };

    useEffect(() => {
        if (language) {
            fetchLessons();
            fetchProgress();
        }
    }, [language]);

    const fetchLessons = async () => {
        try {
            const response = await fetch(`/api/lessons/language/${language}`);
            const data = await response.json();
            if (data.success) {
                setLessons(data.data);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des leçons:', error);
        }
    };

    const fetchProgress = async () => {
        try {
            const response = await fetch(`/api/progress/${language}`);
            const data = await response.json();
            if (data.success) {
                setProgress(data.data);
            }
        } catch (error) {
            console.error('Erreur lors du chargement de la progression:', error);
        } finally {
            setLoading(false);
        }
    };

    const startLesson = (lesson) => {
        setSelectedLesson(lesson);
    };

    const completeLesson = async (lessonId) => {
        try {
            const response = await fetch(`/api/lessons/${lessonId}/complete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });
            const data = await response.json();
            if (data.success) {
                // Rafraîchir la progression
                fetchProgress();
                setSelectedLesson(null);
                // Afficher un message de succès
                alert(`Félicitations ! Vous avez gagné ${data.data.xp_gained} XP !`);
            }
        } catch (error) {
            console.error('Erreur lors de la completion de la leçon:', error);
        }
    };

    const getCategoryIcon = (category) => {
        switch (category) {
            case 'vocabulaire': return '📚';
            case 'grammaire': return '📝';
            case 'conversation': return '💬';
            case 'prononciation': return '🗣️';
            default: return '📖';
        }
    };

    const isLessonCompleted = (lessonId) => {
        return progress?.progress?.completed_lessons?.includes(lessonId) || false;
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <div className="text-white text-2xl">Chargement des leçons...</div>
            </div>
        );
    }

    // Vue détaillée d'une leçon
    if (selectedLesson) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-green-400 to-blue-600 p-8">
                <div className="max-w-4xl mx-auto">
                    {/* Header de la leçon */}
                    <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-8 mb-8">
                        <div className="flex items-center justify-between mb-6">
                            <button 
                                onClick={() => setSelectedLesson(null)}
                                className="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-full font-semibold transition-all"
                            >
                                ← Retour aux leçons
                            </button>
                            <div className="text-6xl">{getCategoryIcon(selectedLesson.category)}</div>
                        </div>
                        
                        <h1 className="text-4xl font-bold text-white mb-4">{selectedLesson.title}</h1>
                        <p className="text-xl text-white/80 mb-6">{selectedLesson.description}</p>
                        
                        <div className="flex items-center space-x-6 text-white/90">
                            <div className="flex items-center space-x-2">
                                <span>🏆</span>
                                <span>{selectedLesson.xp_reward} XP</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <span>📊</span>
                                <span>Niveau {selectedLesson.level}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <span>📂</span>
                                <span className="capitalize">{selectedLesson.category}</span>
                            </div>
                        </div>
                    </div>

                    {/* Contenu de la leçon */}
                    <div className="space-y-8">
                        {/* Vocabulaire */}
                        {selectedLesson.content?.vocabulary && (
                            <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-8">
                                <h2 className="text-3xl font-bold text-white mb-6 flex items-center">
                                    <span className="mr-3">📚</span>
                                    Vocabulaire
                                </h2>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {selectedLesson.content.vocabulary.map((item, index) => (
                                        <div key={index} className="bg-white/10 rounded-xl p-6">
                                            <div className="text-2xl font-bold text-white mb-2">{item.word}</div>
                                            <div className="text-lg text-white/80 mb-2">{item.translation}</div>
                                            {item.pronunciation && (
                                                <div className="text-sm text-white/60 italic">/{item.pronunciation}/</div>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Phrases */}
                        {selectedLesson.content?.phrases && (
                            <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-8">
                                <h2 className="text-3xl font-bold text-white mb-6 flex items-center">
                                    <span className="mr-3">💬</span>
                                    Phrases utiles
                                </h2>
                                <div className="space-y-4">
                                    {selectedLesson.content.phrases.map((item, index) => (
                                        <div key={index} className="bg-white/10 rounded-xl p-6">
                                            <div className="text-xl font-semibold text-white mb-2">{item.phrase}</div>
                                            <div className="text-lg text-white/80">{item.translation}</div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Grammaire */}
                        {selectedLesson.content?.grammar && (
                            <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-8">
                                <h2 className="text-3xl font-bold text-white mb-6 flex items-center">
                                    <span className="mr-3">📝</span>
                                    Grammaire
                                </h2>
                                <div className="bg-white/10 rounded-xl p-6">
                                    <div className="text-lg text-white mb-4">{selectedLesson.content.grammar.rule}</div>
                                    {selectedLesson.content.grammar.examples && (
                                        <div className="space-y-2">
                                            <div className="text-white/80 font-semibold">Exemples :</div>
                                            {selectedLesson.content.grammar.examples.map((example, index) => (
                                                <div key={index} className="text-white/70 ml-4">• {example}</div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* Bouton de completion */}
                        <div className="text-center">
                            <button 
                                onClick={() => completeLesson(selectedLesson.id)}
                                className="bg-green-500 hover:bg-green-600 text-white px-12 py-4 rounded-full text-xl font-bold transform hover:scale-105 transition-all duration-300 shadow-lg"
                                disabled={isLessonCompleted(selectedLesson.id)}
                            >
                                {isLessonCompleted(selectedLesson.id) ? '✅ Leçon terminée' : '🎯 Terminer la leçon'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Vue principale des leçons
    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600">
            <div className="max-w-7xl mx-auto px-4 py-8">
                {/* Header */}
                <div className="text-center mb-12">
                    <div className="text-8xl mb-4">{languageFlags[language]}</div>
                    <h1 className="text-5xl font-bold text-white mb-4">
                        Leçons d'{languageNames[language]}
                    </h1>
                    
                    {/* Progression */}
                    {progress && (
                        <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 max-w-2xl mx-auto">
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-white">
                                <div className="text-center">
                                    <div className="text-2xl font-bold">{progress.progress.current_level}</div>
                                    <div className="text-sm opacity-80">Niveau</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold">{progress.progress.total_xp}</div>
                                    <div className="text-sm opacity-80">XP Total</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold">{progress.progress.streak_days}</div>
                                    <div className="text-sm opacity-80">Jours</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold">{progress.stats.completion_percentage}%</div>
                                    <div className="text-sm opacity-80">Terminé</div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Leçons par niveau */}
                <div className="space-y-12">
                    {Object.entries(lessons).map(([level, levelLessons]) => (
                        <div key={level}>
                            <h2 className="text-3xl font-bold text-white mb-8 flex items-center">
                                <span className="mr-3">🎯</span>
                                Niveau {level}
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {levelLessons.map((lesson) => (
                                    <div 
                                        key={lesson.id}
                                        className={`bg-white/20 backdrop-blur-sm rounded-2xl p-6 cursor-pointer transform hover:scale-105 transition-all duration-300 ${
                                            isLessonCompleted(lesson.id) ? 'ring-4 ring-green-400' : 'hover:bg-white/30'
                                        }`}
                                        onClick={() => startLesson(lesson)}
                                    >
                                        <div className="flex items-center justify-between mb-4">
                                            <div className="text-4xl">{getCategoryIcon(lesson.category)}</div>
                                            {isLessonCompleted(lesson.id) && (
                                                <div className="text-3xl">✅</div>
                                            )}
                                        </div>
                                        
                                        <h3 className="text-xl font-bold text-white mb-2">{lesson.title}</h3>
                                        <p className="text-white/80 mb-4 text-sm">{lesson.description}</p>
                                        
                                        <div className="flex items-center justify-between text-white/70 text-sm">
                                            <span className="capitalize">{lesson.category}</span>
                                            <span>🏆 {lesson.xp_reward} XP</span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))}
                </div>

                {/* Bouton retour */}
                <div className="text-center mt-12">
                    <button 
                        onClick={() => navigate(`/learn/${language}`)}
                        className="bg-white/20 backdrop-blur-sm text-white px-8 py-4 rounded-full font-semibold hover:bg-white/30 transition-all"
                    >
                        ← Retour au dashboard
                    </button>
                </div>
            </div>
        </div>
    );
};

export default Lessons;
