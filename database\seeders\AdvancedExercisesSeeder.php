<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Exercise;

class AdvancedExercisesSeeder extends Seeder
{
    public function run(): void
    {
        // Exercices pour l'Anglais
        $englishExercises = [
            // Choix multiples - Vocabulaire de base
            [
                'language' => 'Anglais',
                'level' => 'Débutant',
                'type' => 'Vocabulaire',
                'title' => 'Couleurs en anglais',
                'description' => 'Apprenez les couleurs de base',
                'exercise_type' => 'multiple_choice',
                'content' => [
                    'question' => 'Comment dit-on "rouge" en anglais ?',
                    'options' => ['Red', 'Blue', 'Green', 'Yellow'],
                    'correct_answer' => 'Red',
                    'explanation' => '"Red" signifie rouge en anglais.'
                ],
                'points' => 10,
                'difficulty' => 1,
                'hint' => 'Pensez à la couleur du sang'
            ],
            [
                'language' => 'Anglais',
                'level' => 'Débutant',
                'type' => 'Vocabulaire',
                'title' => 'Animaux domestiques',
                'description' => 'Vocabulaire des animaux de compagnie',
                'exercise_type' => 'multiple_choice',
                'content' => [
                    'question' => 'Quel animal dit "meow" ?',
                    'options' => ['Dog', 'Cat', 'Bird', 'Fish'],
                    'correct_answer' => 'Cat',
                    'explanation' => 'Les chats font "meow" en anglais.'
                ],
                'points' => 10,
                'difficulty' => 1
            ],
            
            // Traduction - Phrases simples
            [
                'language' => 'Anglais',
                'level' => 'Débutant',
                'type' => 'Traduction',
                'title' => 'Salutations courantes',
                'description' => 'Traduisez les salutations de base',
                'exercise_type' => 'translation',
                'content' => [
                    'text_to_translate' => 'Bonjour, comment allez-vous ?',
                    'correct_answer' => 'Hello, how are you?',
                    'alternatives' => ['Hi, how are you?', 'Hello, how do you do?'],
                    'explanation' => 'Une salutation polie courante en anglais.'
                ],
                'points' => 15,
                'difficulty' => 2,
                'hint' => 'Commencez par "Hello" ou "Hi"'
            ],
            [
                'language' => 'Anglais',
                'level' => 'Débutant',
                'type' => 'Traduction',
                'title' => 'Présentations',
                'description' => 'Se présenter en anglais',
                'exercise_type' => 'translation',
                'content' => [
                    'text_to_translate' => 'Je m\'appelle Marie',
                    'correct_answer' => 'My name is Marie',
                    'alternatives' => ['I am Marie', 'I\'m Marie'],
                    'explanation' => 'Façon courante de se présenter en anglais.'
                ],
                'points' => 15,
                'difficulty' => 2
            ],

            // Complétion de phrases
            [
                'language' => 'Anglais',
                'level' => 'Débutant',
                'type' => 'Grammaire',
                'title' => 'Articles définis',
                'description' => 'Utilisation de "the"',
                'exercise_type' => 'fill_blank',
                'content' => [
                    'sentence' => '_____ sun is shining today.',
                    'correct_answer' => 'The',
                    'options' => ['The', 'A', 'An'],
                    'explanation' => 'On utilise "the" devant "sun" car il n\'y a qu\'un seul soleil.'
                ],
                'points' => 12,
                'difficulty' => 2
            ],

            // Association
            [
                'language' => 'Anglais',
                'level' => 'Débutant',
                'type' => 'Vocabulaire',
                'title' => 'Parties du corps',
                'description' => 'Associez les parties du corps',
                'exercise_type' => 'matching',
                'content' => [
                    'pairs' => [
                        ['left' => 'Head', 'right' => 'Tête'],
                        ['left' => 'Hand', 'right' => 'Main'],
                        ['left' => 'Foot', 'right' => 'Pied'],
                        ['left' => 'Eye', 'right' => 'Œil']
                    ]
                ],
                'points' => 20,
                'difficulty' => 2
            ]
        ];

        // Exercices pour l'Espagnol
        $spanishExercises = [
            [
                'language' => 'Espagnol',
                'level' => 'Débutant',
                'type' => 'Vocabulaire',
                'title' => 'Números básicos',
                'description' => 'Apprenez les nombres de base',
                'exercise_type' => 'multiple_choice',
                'content' => [
                    'question' => 'Comment dit-on "cinq" en espagnol ?',
                    'options' => ['Cuatro', 'Cinco', 'Seis', 'Siete'],
                    'correct_answer' => 'Cinco',
                    'explanation' => '"Cinco" signifie cinq en espagnol.'
                ],
                'points' => 10,
                'difficulty' => 1
            ],
            [
                'language' => 'Espagnol',
                'level' => 'Débutant',
                'type' => 'Traduction',
                'title' => 'Familia',
                'description' => 'Vocabulaire de la famille',
                'exercise_type' => 'translation',
                'content' => [
                    'text_to_translate' => 'Ma mère est très gentille',
                    'correct_answer' => 'Mi madre es muy amable',
                    'alternatives' => ['Mi mamá es muy amable'],
                    'explanation' => '"Madre" ou "mamá" signifient mère en espagnol.'
                ],
                'points' => 15,
                'difficulty' => 2
            ],
            [
                'language' => 'Espagnol',
                'level' => 'Débutant',
                'type' => 'Grammaire',
                'title' => 'Ser vs Estar',
                'description' => 'Différence entre ser et estar',
                'exercise_type' => 'fill_blank',
                'content' => [
                    'sentence' => 'Yo _____ estudiante.',
                    'correct_answer' => 'soy',
                    'options' => ['soy', 'estoy', 'es'],
                    'explanation' => 'On utilise "soy" (ser) pour les caractéristiques permanentes.'
                ],
                'points' => 15,
                'difficulty' => 3
            ]
        ];

        // Exercices pour le Français
        $frenchExercises = [
            [
                'language' => 'Français',
                'level' => 'Débutant',
                'type' => 'Vocabulaire',
                'title' => 'Jours de la semaine',
                'description' => 'Apprenez les jours',
                'exercise_type' => 'multiple_choice',
                'content' => [
                    'question' => 'Quel jour vient après mardi ?',
                    'options' => ['Lundi', 'Mercredi', 'Jeudi', 'Vendredi'],
                    'correct_answer' => 'Mercredi',
                    'explanation' => 'Mercredi vient après mardi dans la semaine.'
                ],
                'points' => 10,
                'difficulty' => 1
            ],
            [
                'language' => 'Français',
                'level' => 'Débutant',
                'type' => 'Grammaire',
                'title' => 'Articles définis',
                'description' => 'Le, la, les',
                'exercise_type' => 'fill_blank',
                'content' => [
                    'sentence' => '_____ chat est mignon.',
                    'correct_answer' => 'Le',
                    'options' => ['Le', 'La', 'Les'],
                    'explanation' => '"Chat" est masculin, donc on utilise "le".'
                ],
                'points' => 12,
                'difficulty' => 2
            ]
        ];

        // Exercices pour l'Allemand
        $germanExercises = [
            [
                'language' => 'Allemand',
                'level' => 'Débutant',
                'type' => 'Vocabulaire',
                'title' => 'Begrüßungen',
                'description' => 'Salutations allemandes',
                'exercise_type' => 'multiple_choice',
                'content' => [
                    'question' => 'Comment dit-on "bonjour" en allemand ?',
                    'options' => ['Guten Tag', 'Auf Wiedersehen', 'Danke', 'Bitte'],
                    'correct_answer' => 'Guten Tag',
                    'explanation' => '"Guten Tag" est une salutation courante en allemand.'
                ],
                'points' => 10,
                'difficulty' => 1
            ],
            [
                'language' => 'Allemand',
                'level' => 'Débutant',
                'type' => 'Grammaire',
                'title' => 'Articles définis',
                'description' => 'Der, die, das',
                'exercise_type' => 'fill_blank',
                'content' => [
                    'sentence' => '_____ Hund ist groß.',
                    'correct_answer' => 'Der',
                    'options' => ['Der', 'Die', 'Das'],
                    'explanation' => '"Hund" est masculin, donc on utilise "der".'
                ],
                'points' => 15,
                'difficulty' => 3
            ]
        ];

        // Exercices pour l'Italien
        $italianExercises = [
            [
                'language' => 'Italien',
                'level' => 'Débutant',
                'type' => 'Vocabulaire',
                'title' => 'Cibo italiano',
                'description' => 'Nourriture italienne',
                'exercise_type' => 'multiple_choice',
                'content' => [
                    'question' => 'Qu\'est-ce que "pizza" en français ?',
                    'options' => ['Pizza', 'Pâtes', 'Pain', 'Fromage'],
                    'correct_answer' => 'Pizza',
                    'explanation' => '"Pizza" se dit pareil en français et en italien.'
                ],
                'points' => 8,
                'difficulty' => 1
            ],
            [
                'language' => 'Italien',
                'level' => 'Débutant',
                'type' => 'Traduction',
                'title' => 'Presentazioni',
                'description' => 'Se présenter en italien',
                'exercise_type' => 'translation',
                'content' => [
                    'text_to_translate' => 'Je suis français',
                    'correct_answer' => 'Sono francese',
                    'alternatives' => ['Io sono francese'],
                    'explanation' => '"Sono" signifie "je suis" en italien.'
                ],
                'points' => 15,
                'difficulty' => 2
            ]
        ];

        // Combiner tous les exercices
        $allExercises = array_merge(
            $englishExercises,
            $spanishExercises, 
            $frenchExercises,
            $germanExercises,
            $italianExercises
        );

        // Insérer tous les exercices
        foreach ($allExercises as $exercise) {
            Exercise::create([
                'language' => $exercise['language'],
                'level' => $exercise['level'],
                'type' => $exercise['type'],
                'title' => $exercise['title'],
                'description' => $exercise['description'],
                'content' => $exercise['content'],
                'difficulty' => $exercise['difficulty'],
                'points' => $exercise['points'],
                'is_active' => true,
                'exercise_type' => $exercise['exercise_type'],
                'hint' => $exercise['hint'] ?? null,
                'instruction' => $exercise['instruction'] ?? null
            ]);
        }

        $this->command->info('Exercices avancés créés avec succès !');
        $this->command->info('Total: ' . count($allExercises) . ' exercices ajoutés');
    }
}
