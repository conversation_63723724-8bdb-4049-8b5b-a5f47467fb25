<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\LearningPath;
use App\Models\Lesson;
use App\Models\Exercise;

class LearningPathsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer les IDs des leçons et exercices existants
        $englishLessons = Lesson::where('language', 'Anglais')->pluck('id')->toArray();
        $englishExercises = Exercise::where('language', 'Anglais')->pluck('id')->toArray();

        $spanishLessons = Lesson::where('language', 'Espagnol')->pluck('id')->toArray();
        $spanishExercises = Exercise::where('language', 'Espagnol')->pluck('id')->toArray();

        $frenchLessons = Lesson::where('language', 'Français')->pluck('id')->toArray();
        $frenchExercises = Exercise::where('language', 'Français')->pluck('id')->toArray();

        // Parcours Anglais
        LearningPath::create([
            'name' => 'Anglais pour Débutants',
            'language' => 'Anglais',
            'description' => 'Apprenez les bases de l\'anglais avec ce parcours complet pour débutants.',
            'level' => 'Débutant',
            'category' => 'Général',
            'lesson_sequence' => array_slice($englishLessons, 0, 3),
            'exercise_sequence' => array_slice($englishExercises, 0, 5),
            'estimated_duration_hours' => 20,
            'difficulty_progression' => 'linear',
            'prerequisites' => [],
            'learning_objectives' => [
                'Maîtriser les salutations de base',
                'Comprendre les verbes être et avoir',
                'Construire des phrases simples',
                'Acquérir du vocabulaire essentiel'
            ],
            'icon' => '🇬🇧',
            'color' => '#3b82f6',
            'completion_reward_xp' => 500
        ]);

        LearningPath::create([
            'name' => 'Grammaire Anglaise Essentielle',
            'language' => 'Anglais',
            'description' => 'Maîtrisez les règles grammaticales fondamentales de l\'anglais.',
            'level' => 'Intermédiaire',
            'category' => 'Grammaire',
            'lesson_sequence' => array_slice($englishLessons, 1, 2),
            'exercise_sequence' => array_slice($englishExercises, 2, 3),
            'estimated_duration_hours' => 15,
            'difficulty_progression' => 'adaptive',
            'prerequisites' => ['Anglais pour Débutants'],
            'learning_objectives' => [
                'Maîtriser les temps verbaux',
                'Comprendre la structure des phrases',
                'Utiliser les articles correctement'
            ],
            'icon' => '📚',
            'color' => '#10b981',
            'completion_reward_xp' => 300
        ]);

        // Parcours Espagnol
        LearningPath::create([
            'name' => 'Espagnol pour Débutants',
            'language' => 'Espagnol',
            'description' => 'Découvrez l\'espagnol avec ce parcours adapté aux débutants complets.',
            'level' => 'Débutant',
            'category' => 'Général',
            'lesson_sequence' => array_slice($spanishLessons, 0, 3),
            'exercise_sequence' => array_slice($spanishExercises, 0, 4),
            'estimated_duration_hours' => 18,
            'difficulty_progression' => 'linear',
            'prerequisites' => [],
            'learning_objectives' => [
                'Apprendre les salutations espagnoles',
                'Maîtriser ser et estar',
                'Compter jusqu\'à 100',
                'Parler de sa famille'
            ],
            'icon' => '🇪🇸',
            'color' => '#f59e0b',
            'completion_reward_xp' => 450
        ]);

        // Parcours Français
        LearningPath::create([
            'name' => 'Français pour Débutants',
            'language' => 'Français',
            'description' => 'Commencez votre apprentissage du français avec ce parcours structuré.',
            'level' => 'Débutant',
            'category' => 'Général',
            'lesson_sequence' => array_slice($frenchLessons, 0, 2),
            'exercise_sequence' => array_slice($frenchExercises, 0, 3),
            'estimated_duration_hours' => 16,
            'difficulty_progression' => 'linear',
            'prerequisites' => [],
            'learning_objectives' => [
                'Connaître les jours de la semaine',
                'Utiliser les articles définis',
                'Construire des phrases de base'
            ],
            'icon' => '🇫🇷',
            'color' => '#8b5cf6',
            'completion_reward_xp' => 400
        ]);

        // Parcours spécialisés
        LearningPath::create([
            'name' => 'Vocabulaire Quotidien Multilingue',
            'language' => 'Multilingue',
            'description' => 'Apprenez le vocabulaire essentiel dans plusieurs langues.',
            'level' => 'Débutant',
            'category' => 'Vocabulaire',
            'lesson_sequence' => [],
            'exercise_sequence' => array_merge(
                array_slice($englishExercises, 0, 2),
                array_slice($spanishExercises, 0, 2),
                array_slice($frenchExercises, 0, 2)
            ),
            'estimated_duration_hours' => 12,
            'difficulty_progression' => 'adaptive',
            'prerequisites' => [],
            'learning_objectives' => [
                'Maîtriser les couleurs dans 3 langues',
                'Connaître les animaux de base',
                'Apprendre les salutations universelles'
            ],
            'icon' => '🌍',
            'color' => '#ef4444',
            'completion_reward_xp' => 600
        ]);

        LearningPath::create([
            'name' => 'Révision Intensive',
            'language' => 'Multilingue',
            'description' => 'Parcours de révision pour consolider vos acquis dans toutes les langues.',
            'level' => 'Tous niveaux',
            'category' => 'Révision',
            'lesson_sequence' => [],
            'exercise_sequence' => array_merge($englishExercises, $spanishExercises, $frenchExercises),
            'estimated_duration_hours' => 25,
            'difficulty_progression' => 'adaptive',
            'prerequisites' => [],
            'learning_objectives' => [
                'Consolider toutes les connaissances acquises',
                'Identifier les points faibles',
                'Améliorer la rétention à long terme'
            ],
            'icon' => '🔄',
            'color' => '#6366f1',
            'completion_reward_xp' => 1000
        ]);

        echo "✅ Parcours d'apprentissage créés avec succès !\n";
    }
}
