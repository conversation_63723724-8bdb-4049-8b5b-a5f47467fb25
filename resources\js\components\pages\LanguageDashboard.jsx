import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

const LanguageDashboard = () => {
    const { language } = useParams();
    const navigate = useNavigate();
    const [progress, setProgress] = useState(null);
    const [achievements, setAchievements] = useState([]);
    const [loading, setLoading] = useState(true);

    const languageNames = {
        'en': 'Anglais',
        'es': 'Espagnol', 
        'fr': 'Français',
        'de': 'Allemand',
        'it': 'Italien',
        'pt': 'Portugais',
        'zh': 'Chinois',
        'ja': 'Japonais'
    };

    const languageFlags = {
        'en': '🇬🇧',
        'es': '🇪🇸',
        'fr': '🇫🇷', 
        'de': '🇩🇪',
        'it': '🇮🇹',
        'pt': '🇵🇹',
        'zh': '🇨🇳',
        'ja': '🇯🇵'
    };

    const languageColors = {
        'en': 'from-blue-500 to-blue-600',
        'es': 'from-red-500 to-red-600',
        'fr': 'from-blue-600 to-red-500',
        'de': 'from-gray-700 to-red-600',
        'it': 'from-green-500 to-red-500',
        'pt': 'from-green-600 to-red-600',
        'zh': 'from-red-600 to-yellow-500',
        'ja': 'from-red-500 to-white'
    };

    useEffect(() => {
        if (language) {
            fetchProgress();
            fetchAchievements();
        }
    }, [language]);

    const fetchProgress = async () => {
        try {
            const response = await fetch(`/api/progress/${language}`);
            const data = await response.json();
            if (data.success) {
                setProgress(data.data);
            }
        } catch (error) {
            console.error('Erreur lors du chargement de la progression:', error);
        }
    };

    const fetchAchievements = async () => {
        try {
            const response = await fetch(`/api/achievements/${language}`);
            const data = await response.json();
            if (data.success) {
                setAchievements(data.data);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des achievements:', error);
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className={`min-h-screen bg-gradient-to-br ${languageColors[language]} flex items-center justify-center`}>
                <div className="text-white text-2xl">Chargement du dashboard...</div>
            </div>
        );
    }

    return (
        <div className={`min-h-screen bg-gradient-to-br ${languageColors[language]}`}>
            <div className="max-w-7xl mx-auto px-4 py-8">
                {/* Header */}
                <div className="text-center mb-12">
                    <div className="text-8xl mb-4">{languageFlags[language]}</div>
                    <h1 className="text-5xl font-bold text-white mb-4">
                        Dashboard {languageNames[language]}
                    </h1>
                    <p className="text-xl text-white/80">
                        Continuez votre apprentissage et atteignez vos objectifs !
                    </p>
                </div>

                {/* Statistiques de progression */}
                {progress && (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                        <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 text-center">
                            <div className="text-4xl mb-2">🎯</div>
                            <div className="text-3xl font-bold text-white">{progress.progress.current_level}</div>
                            <div className="text-white/80">Niveau actuel</div>
                        </div>
                        
                        <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 text-center">
                            <div className="text-4xl mb-2">⭐</div>
                            <div className="text-3xl font-bold text-white">{progress.progress.total_xp}</div>
                            <div className="text-white/80">XP Total</div>
                        </div>
                        
                        <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 text-center">
                            <div className="text-4xl mb-2">🔥</div>
                            <div className="text-3xl font-bold text-white">{progress.progress.streak_days}</div>
                            <div className="text-white/80">Jours consécutifs</div>
                        </div>
                        
                        <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 text-center">
                            <div className="text-4xl mb-2">📊</div>
                            <div className="text-3xl font-bold text-white">{progress.stats.completion_percentage}%</div>
                            <div className="text-white/80">Progression</div>
                        </div>
                    </div>
                )}

                {/* Barre de progression du niveau */}
                {progress && (
                    <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 mb-12">
                        <div className="flex items-center justify-between mb-4">
                            <h2 className="text-2xl font-bold text-white">Progression du niveau</h2>
                            <span className="text-white/80">
                                {progress.stats.xp_progress} / 100 XP
                            </span>
                        </div>
                        <div className="w-full bg-white/20 rounded-full h-4">
                            <div 
                                className="bg-gradient-to-r from-green-400 to-green-600 h-4 rounded-full transition-all duration-500"
                                style={{ width: `${progress.stats.level_progress_percentage}%` }}
                            ></div>
                        </div>
                        <div className="text-white/70 text-sm mt-2">
                            Plus que {100 - progress.stats.xp_progress} XP pour atteindre le niveau {progress.progress.current_level + 1} !
                        </div>
                    </div>
                )}

                {/* Actions principales */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                    <div 
                        className="bg-white/20 backdrop-blur-sm rounded-2xl p-8 cursor-pointer transform hover:scale-105 transition-all duration-300 hover:bg-white/30"
                        onClick={() => navigate(`/learn/${language}/lessons`)}
                    >
                        <div className="text-6xl mb-4 text-center">📚</div>
                        <h3 className="text-2xl font-bold text-white mb-2 text-center">Leçons</h3>
                        <p className="text-white/80 text-center">
                            Apprenez de nouveaux mots et concepts
                        </p>
                        <div className="mt-4 text-center">
                            <span className="bg-white/20 text-white px-4 py-2 rounded-full text-sm">
                                {progress?.stats.completed_lessons || 0} / {progress?.stats.total_lessons || 0} terminées
                            </span>
                        </div>
                    </div>

                    <div 
                        className="bg-white/20 backdrop-blur-sm rounded-2xl p-8 cursor-pointer transform hover:scale-105 transition-all duration-300 hover:bg-white/30"
                        onClick={() => navigate(`/learn/${language}/exercises`)}
                    >
                        <div className="text-6xl mb-4 text-center">✏️</div>
                        <h3 className="text-2xl font-bold text-white mb-2 text-center">Exercices</h3>
                        <p className="text-white/80 text-center">
                            Pratiquez avec des exercices interactifs
                        </p>
                        <div className="mt-4 text-center">
                            <span className="bg-white/20 text-white px-4 py-2 rounded-full text-sm">
                                Exercices interactifs
                            </span>
                        </div>
                    </div>

                    <div 
                        className="bg-white/20 backdrop-blur-sm rounded-2xl p-8 cursor-pointer transform hover:scale-105 transition-all duration-300 hover:bg-white/30"
                        onClick={() => navigate(`/learn/${language}/review`)}
                    >
                        <div className="text-6xl mb-4 text-center">🔄</div>
                        <h3 className="text-2xl font-bold text-white mb-2 text-center">Révision</h3>
                        <p className="text-white/80 text-center">
                            Révisez ce que vous avez appris
                        </p>
                        <div className="mt-4 text-center">
                            <span className="bg-white/20 text-white px-4 py-2 rounded-full text-sm">
                                Disponible bientôt
                            </span>
                        </div>
                    </div>
                </div>

                {/* Achievements */}
                <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-8 mb-12">
                    <h2 className="text-3xl font-bold text-white mb-6 flex items-center">
                        <span className="mr-3">🏆</span>
                        Achievements
                    </h2>
                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
                        {achievements.map((achievement) => (
                            <div 
                                key={achievement.key}
                                className={`text-center p-4 rounded-xl transition-all ${
                                    achievement.unlocked 
                                        ? 'bg-yellow-400/20 text-white' 
                                        : 'bg-white/10 text-white/50'
                                }`}
                                title={achievement.description}
                            >
                                <div className="text-3xl mb-2">{achievement.icon}</div>
                                <div className="text-xs font-semibold">{achievement.name}</div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Objectif quotidien */}
                <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-8 mb-12">
                    <h2 className="text-3xl font-bold text-white mb-6 flex items-center">
                        <span className="mr-3">🎯</span>
                        Objectif quotidien
                    </h2>
                    <div className="flex items-center justify-between">
                        <div>
                            <div className="text-white text-lg">
                                XP aujourd'hui : <span className="font-bold">{progress?.progress.daily_xp || 0}</span> / 50
                            </div>
                            <div className="text-white/70 text-sm">
                                Continuez pour maintenir votre série !
                            </div>
                        </div>
                        <div className="text-6xl">
                            {(progress?.progress.daily_xp || 0) >= 50 ? '✅' : '⏰'}
                        </div>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-3 mt-4">
                        <div 
                            className="bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-500"
                            style={{ width: `${Math.min(((progress?.progress.daily_xp || 0) / 50) * 100, 100)}%` }}
                        ></div>
                    </div>
                </div>

                {/* Navigation */}
                <div className="text-center">
                    <button 
                        onClick={() => navigate('/learn')}
                        className="bg-white/20 backdrop-blur-sm text-white px-8 py-4 rounded-full font-semibold hover:bg-white/30 transition-all mr-4"
                    >
                        ← Choisir une autre langue
                    </button>
                    <button 
                        onClick={() => navigate(`/learn/${language}/lessons`)}
                        className="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-semibold transition-all"
                    >
                        Commencer à apprendre →
                    </button>
                </div>
            </div>
        </div>
    );
};

export default LanguageDashboard;
