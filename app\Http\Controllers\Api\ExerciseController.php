<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Exercise;
use App\Models\UserExerciseProgress;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ExerciseController extends Controller
{
    /**
     * Récupérer tous les exercices avec filtres optionnels
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Exercise::active();

            // Filtrer par langue
            if ($request->has('language') && $request->language) {
                $query->byLanguage($request->language);
            }

            // Filtrer par niveau
            if ($request->has('level') && $request->level) {
                $query->byLevel($request->level);
            }

            // Filtrer par type
            if ($request->has('type') && $request->type) {
                $query->byType($request->type);
            }

            // Trier par difficulté par défaut
            $query->orderBy('difficulty', 'asc')->orderBy('title', 'asc');

            $exercises = $query->get();

            return response()->json([
                'success' => true,
                'data' => $exercises,
                'message' => 'Exercices récupérés avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des exercices',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupérer un exercice spécifique
     */
    public function show($id): JsonResponse
    {
        try {
            $exercise = Exercise::active()->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $exercise,
                'message' => 'Exercice récupéré avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Exercice non trouvé',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Soumettre les réponses d'un exercice
     */
    public function submitAnswers(Request $request, $id): JsonResponse
    {
        try {
            $exercise = Exercise::active()->findOrFail($id);
            
            $request->validate([
                'answers' => 'required|array',
                'user_id' => 'nullable|exists:users,id'
            ]);

            // Calculer le score
            $score = 0;
            $maxScore = count($exercise->content['questions']);
            $userAnswers = $request->answers;

            foreach ($exercise->content['questions'] as $index => $question) {
                if (isset($userAnswers[$index]) && $userAnswers[$index] == $question['correct']) {
                    $score++;
                }
            }

            // Enregistrer le progrès si l'utilisateur est connecté
            if ($request->user_id) {
                UserExerciseProgress::updateOrCreate(
                    [
                        'user_id' => $request->user_id,
                        'exercise_id' => $exercise->id
                    ],
                    [
                        'answers' => $userAnswers,
                        'score' => $score,
                        'max_score' => $maxScore,
                        'completed' => true,
                        'completed_at' => now()
                    ]
                );
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'score' => $score,
                    'max_score' => $maxScore,
                    'percentage' => round(($score / $maxScore) * 100, 2),
                    'points_earned' => round(($score / $maxScore) * $exercise->points)
                ],
                'message' => 'Réponses soumises avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la soumission des réponses',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupérer les exercices par langue
     */
    public function getByLanguage($language): JsonResponse
    {
        try {
            $exercises = Exercise::active()
                ->byLanguage($language)
                ->orderBy('level')
                ->orderBy('difficulty')
                ->get()
                ->groupBy(['level', 'type']);

            return response()->json([
                'success' => true,
                'data' => $exercises,
                'message' => "Exercices pour {$language} récupérés avec succès"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des exercices',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
