import React from 'react';
import { useNavigate } from 'react-router-dom';

const Dashboard = () => {
    const navigate = useNavigate();

    const languages = [
        { code: 'en', name: '<PERSON><PERSON><PERSON>', flag: '🇬🇧' },
        { code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' },
        { code: 'fr', name: 'Français', flag: '🇫🇷' },
        { code: 'de', name: '<PERSON>eman<PERSON>', flag: '🇩🇪' },
        { code: 'it', name: 'Itali<PERSON>', flag: '🇮🇹' }
    ];

    return (
        <div className="min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-pink-600">
            <div className="container mx-auto px-4 py-8">
                {/* Header */}
                <div className="text-center mb-12">
                    <h1 className="text-5xl font-bold text-white mb-4">
                        🌟 Learnify
                    </h1>
                    <p className="text-xl text-white/80 max-w-3xl mx-auto">
                        Plateforme d'apprentissage des langues avec système de répétition espacée, 
                        parcours structurés et exercices interactifs
                    </p>
                </div>

                {/* Langues disponibles */}
                <div className="mb-16">
                    <h2 className="text-3xl font-bold text-white mb-8 text-center">Choisissez votre langue</h2>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
                        {languages.map((lang) => (
                            <div
                                key={lang.code}
                                className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 cursor-pointer transform hover:scale-105 transition-all duration-300 hover:bg-white/30"
                                onClick={() => navigate(`/learn/${lang.code}`)}
                            >
                                <div className="text-6xl mb-4 text-center">{lang.flag}</div>
                                <h3 className="text-xl font-bold text-white mb-2 text-center">{lang.name}</h3>
                                <p className="text-white/80 text-center">
                                    Commencer l'apprentissage
                                </p>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Parcours d'apprentissage */}
                <div className="mb-16">
                    <h2 className="text-3xl font-bold text-white mb-8 text-center">Parcours d'apprentissage structurés</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div
                            className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl p-8 cursor-pointer transform hover:scale-105 transition-all duration-300 shadow-2xl"
                            onClick={() => navigate('/learning-paths')}
                        >
                            <div className="text-center">
                                <div className="text-6xl mb-4">🎯</div>
                                <h3 className="text-2xl font-bold text-white mb-2">Tous les parcours</h3>
                                <p className="text-white/80 mb-4">
                                    Découvrez tous nos parcours d'apprentissage structurés avec progression guidée
                                </p>
                                <div className="flex justify-center space-x-2">
                                    <span className="bg-white/20 text-white px-3 py-1 rounded-full text-sm">
                                        Apprentissage guidé
                                    </span>
                                    <span className="bg-white/20 text-white px-3 py-1 rounded-full text-sm">
                                        Progression suivie
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div
                            className="bg-gradient-to-br from-green-500 to-teal-600 rounded-3xl p-8 cursor-pointer transform hover:scale-105 transition-all duration-300 shadow-2xl"
                            onClick={() => navigate('/learning-paths/all')}
                        >
                            <div className="text-center">
                                <div className="text-6xl mb-4">🌍</div>
                                <h3 className="text-2xl font-bold text-white mb-2">Parcours multilingues</h3>
                                <p className="text-white/80 mb-4">
                                    Apprenez plusieurs langues avec des parcours adaptés et du contenu varié
                                </p>
                                <div className="flex justify-center space-x-2">
                                    <span className="bg-white/20 text-white px-3 py-1 rounded-full text-sm">
                                        Polyglotte
                                    </span>
                                    <span className="bg-white/20 text-white px-3 py-1 rounded-full text-sm">
                                        Révision intensive
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Fonctionnalités principales */}
                <div className="mb-16">
                    <h2 className="text-3xl font-bold text-white mb-8 text-center">Fonctionnalités d'apprentissage</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 text-center">
                            <div className="text-5xl mb-4">📚</div>
                            <h3 className="text-xl font-bold text-white mb-2">Leçons interactives</h3>
                            <p className="text-white/80">
                                Apprenez avec des leçons structurées et du contenu riche
                            </p>
                        </div>

                        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 text-center">
                            <div className="text-5xl mb-4">🧠</div>
                            <h3 className="text-xl font-bold text-white mb-2">Répétition espacée</h3>
                            <p className="text-white/80">
                                Système SM-2 pour optimiser la mémorisation à long terme
                            </p>
                        </div>

                        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 text-center">
                            <div className="text-5xl mb-4">🎮</div>
                            <h3 className="text-xl font-bold text-white mb-2">Exercices gamifiés</h3>
                            <p className="text-white/80">
                                Exercices variés avec système de points et de progression
                            </p>
                        </div>

                        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 text-center">
                            <div className="text-5xl mb-4">📊</div>
                            <h3 className="text-xl font-bold text-white mb-2">Suivi de progression</h3>
                            <p className="text-white/80">
                                Statistiques détaillées et suivi de vos performances
                            </p>
                        </div>

                        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 text-center">
                            <div className="text-5xl mb-4">🏆</div>
                            <h3 className="text-xl font-bold text-white mb-2">Système de récompenses</h3>
                            <p className="text-white/80">
                                Gagnez des points XP et débloquez des achievements
                            </p>
                        </div>

                        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 text-center">
                            <div className="text-5xl mb-4">🎯</div>
                            <h3 className="text-xl font-bold text-white mb-2">Apprentissage adaptatif</h3>
                            <p className="text-white/80">
                                Contenu qui s'adapte à votre niveau et vos besoins
                            </p>
                        </div>
                    </div>
                </div>

                {/* Accès rapide */}
                <div className="text-center">
                    <h2 className="text-3xl font-bold text-white mb-8">Accès rapide</h2>
                    <div className="flex flex-wrap justify-center gap-4">
                        <button
                            onClick={() => navigate('/learning-paths')}
                            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-xl transition-all duration-200"
                        >
                            🎯 Parcours d'apprentissage
                        </button>
                        
                        <button
                            onClick={() => navigate('/learn/en')}
                            className="bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-xl transition-all duration-200"
                        >
                            🇬🇧 Apprendre l'anglais
                        </button>
                        
                        <button
                            onClick={() => navigate('/learn/es')}
                            className="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-6 rounded-xl transition-all duration-200"
                        >
                            🇪🇸 Apprendre l'espagnol
                        </button>
                        
                        <button
                            onClick={() => navigate('/learn/fr')}
                            className="bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-6 rounded-xl transition-all duration-200"
                        >
                            🇫🇷 Apprendre le français
                        </button>
                    </div>
                </div>

                {/* Footer */}
                <div className="mt-16 text-center">
                    <p className="text-white/60">
                        © 2025 Learnify - Plateforme d'apprentissage des langues gratuite
                    </p>
                </div>
            </div>
        </div>
    );
};

export default Dashboard;
