<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Course;
use Carbon\Carbon;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $courses = [
            [
                'language' => 'Anglais',
                'level' => 'Débutant',
                'title' => 'Anglais pour débutants',
                'description' => 'Apprenez les bases de l\'anglais : vocabulaire essentiel, grammaire de base, et conversation simple. Idéal pour commencer votre apprentissage de l\'anglais.',
                'duration_weeks' => 8,
                'available_spots' => 12,
                'max_spots' => 12,
                'start_date' => Carbon::parse('2025-07-15'),
                'format' => 'Visioconférence',
                'price' => 0.00,
                'is_active' => true
            ],
            [
                'language' => 'Espagnol',
                'level' => 'Intermédiaire',
                'title' => 'Espagnol intermédiaire',
                'description' => 'Perfectionnez votre espagnol avec des conversations avancées, la grammaire complexe et l\'expression écrite. Pour ceux qui ont déjà des bases.',
                'duration_weeks' => 6,
                'available_spots' => 8,
                'max_spots' => 8,
                'start_date' => Carbon::parse('2025-07-22'),
                'format' => 'Présentiel',
                'price' => 0.00,
                'is_active' => true
            ],
            [
                'language' => 'Italien',
                'level' => 'Avancé',
                'title' => 'Italien avancé',
                'description' => 'Maîtrisez l\'italien avec des discussions complexes, la littérature italienne et la rédaction professionnelle. Niveau avancé requis.',
                'duration_weeks' => 10,
                'available_spots' => 6,
                'max_spots' => 6,
                'start_date' => Carbon::parse('2025-08-01'),
                'format' => 'Hybride',
                'price' => 0.00,
                'is_active' => true
            ],
            [
                'language' => 'Français',
                'level' => 'Débutant',
                'title' => 'Français langue étrangère',
                'description' => 'Découvrez la langue française : prononciation, vocabulaire de base, grammaire essentielle et culture française.',
                'duration_weeks' => 8,
                'available_spots' => 10,
                'max_spots' => 10,
                'start_date' => Carbon::parse('2025-08-10'),
                'format' => 'Visioconférence',
                'price' => 0.00,
                'is_active' => true
            ],
            [
                'language' => 'Allemand',
                'level' => 'Débutant',
                'title' => 'Allemand pour débutants',
                'description' => 'Initiez-vous à l\'allemand : alphabet, prononciation, vocabulaire de base et structures grammaticales fondamentales.',
                'duration_weeks' => 5,
                'available_spots' => 7,
                'max_spots' => 7,
                'start_date' => Carbon::parse('2025-08-17'),
                'format' => 'Présentiel',
                'price' => 0.00,
                'is_active' => true
            ]
        ];

        foreach ($courses as $courseData) {
            Course::create($courseData);
        }
    }
}
