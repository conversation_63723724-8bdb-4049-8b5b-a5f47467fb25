<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_reviews', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->default(1); // ID utilisateur par défaut
            $table->unsignedBigInteger('exercise_id');
            $table->string('language');
            $table->integer('review_count')->default(0); // Nombre de révisions
            $table->integer('correct_count')->default(0); // Nombre de bonnes réponses
            $table->integer('incorrect_count')->default(0); // Nombre de mauvaises réponses
            $table->float('ease_factor')->default(2.5); // Facteur de facilité (algorithme SM-2)
            $table->integer('interval_days')->default(1); // Intervalle en jours
            $table->timestamp('next_review_at')->nullable(); // Prochaine révision
            $table->timestamp('last_reviewed_at')->nullable(); // Dernière révision
            $table->json('performance_history')->nullable(); // Historique des performances
            $table->boolean('is_mastered')->default(false); // Exercice maîtrisé
            $table->timestamps();

            $table->foreign('exercise_id')->references('id')->on('exercises')->onDelete('cascade');
            $table->unique(['user_id', 'exercise_id']);
            $table->index(['user_id', 'language']);
            $table->index(['next_review_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_reviews');
    }
};
