<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            // Modifier la colonne price pour avoir une valeur par défaut de 0.00
            $table->decimal('price', 8, 2)->default(0.00)->change();
        });

        // Mettre à jour tous les cours existants pour qu'ils soient gratuits
        DB::table('courses')->update(['price' => 0.00]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            // Remettre la colonne price comme nullable sans valeur par défaut
            $table->decimal('price', 8, 2)->nullable()->default(null)->change();
        });
    }
};
