import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { exerciseService } from '../../services/exerciseApi';

function ExerciseDetail() {
    const { id } = useParams();
    const navigate = useNavigate();
    
    const [exercise, setExercise] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [currentQuestion, setCurrentQuestion] = useState(0);
    const [answers, setAnswers] = useState({});
    const [showResults, setShowResults] = useState(false);
    const [results, setResults] = useState(null);
    const [submitting, setSubmitting] = useState(false);

    useEffect(() => {
        loadExercise();
    }, [id]);

    const loadExercise = async () => {
        try {
            setLoading(true);
            const response = await exerciseService.getExercise(id);
            if (response.success) {
                setExercise(response.data);
            } else {
                setError('Exercice non trouvé');
            }
        } catch (err) {
            setError('Erreur lors du chargement de l\'exercice');
            console.error('Erreur:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleAnswerSelect = (questionIndex, answerIndex) => {
        setAnswers(prev => ({
            ...prev,
            [questionIndex]: answerIndex
        }));
    };

    const handleNext = () => {
        if (currentQuestion < exercise.content.questions.length - 1) {
            setCurrentQuestion(prev => prev + 1);
        }
    };

    const handlePrevious = () => {
        if (currentQuestion > 0) {
            setCurrentQuestion(prev => prev - 1);
        }
    };

    const handleSubmit = async () => {
        try {
            setSubmitting(true);
            const response = await exerciseService.submitAnswers(exercise.id, answers);
            if (response.success) {
                setResults(response.data);
                setShowResults(true);
            } else {
                setError('Erreur lors de la soumission des réponses');
            }
        } catch (err) {
            setError('Erreur lors de la soumission');
            console.error('Erreur:', err);
        } finally {
            setSubmitting(false);
        }
    };

    const resetExercise = () => {
        setCurrentQuestion(0);
        setAnswers({});
        setShowResults(false);
        setResults(null);
    };

    const getTypeIcon = (type) => {
        switch (type) {
            case 'Vocabulaire':
                return '📚';
            case 'Grammaire':
                return '📝';
            case 'Écoute':
                return '🎧';
            case 'Lecture':
                return '📖';
            case 'Expression':
                return '💬';
            default:
                return '🎯';
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Chargement de l'exercice...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="text-red-500 text-6xl mb-4">⚠️</div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">Erreur</h2>
                    <p className="text-gray-600 mb-4">{error}</p>
                    <Link
                        to="/exercises"
                        className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
                    >
                        Retour aux exercices
                    </Link>
                </div>
            </div>
        );
    }

    if (!exercise) {
        return null;
    }

    // Affichage des résultats
    if (showResults && results) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-8">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="bg-white rounded-lg shadow-lg p-8 text-center">
                        <div className="text-6xl mb-6">
                            {results.percentage >= 80 ? '🎉' : results.percentage >= 60 ? '👍' : '💪'}
                        </div>
                        
                        <h2 className="text-3xl font-bold text-gray-800 mb-4">
                            Exercice terminé !
                        </h2>
                        
                        <div className="bg-gray-50 rounded-lg p-6 mb-6">
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                                <div>
                                    <div className="text-2xl font-bold text-blue-600">{results.score}</div>
                                    <div className="text-sm text-gray-600">Bonnes réponses</div>
                                </div>
                                <div>
                                    <div className="text-2xl font-bold text-gray-600">{results.max_score}</div>
                                    <div className="text-sm text-gray-600">Questions totales</div>
                                </div>
                                <div>
                                    <div className="text-2xl font-bold text-green-600">{results.percentage}%</div>
                                    <div className="text-sm text-gray-600">Score</div>
                                </div>
                                <div>
                                    <div className="text-2xl font-bold text-purple-600">{results.points_earned}</div>
                                    <div className="text-sm text-gray-600">Points gagnés</div>
                                </div>
                            </div>
                        </div>

                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <button
                                onClick={resetExercise}
                                className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors font-medium"
                            >
                                Refaire l'exercice
                            </button>
                            <Link
                                to="/exercises"
                                className="bg-gray-600 text-white px-6 py-3 rounded-md hover:bg-gray-700 transition-colors font-medium"
                            >
                                Autres exercices
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    const currentQ = exercise.content.questions[currentQuestion];
    const progress = ((currentQuestion + 1) / exercise.content.questions.length) * 100;
    const allAnswered = exercise.content.questions.every((_, index) => answers.hasOwnProperty(index));

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-8">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* En-tête de l'exercice */}
                <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                            <span className="text-3xl">{getTypeIcon(exercise.type)}</span>
                            <div>
                                <h1 className="text-2xl font-bold text-gray-800">{exercise.title}</h1>
                                <p className="text-gray-600">{exercise.language} - {exercise.level} - {exercise.type}</p>
                            </div>
                        </div>
                        <Link
                            to="/exercises"
                            className="text-gray-500 hover:text-gray-700 transition-colors"
                        >
                            ✕ Fermer
                        </Link>
                    </div>
                    
                    {/* Barre de progression */}
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                        <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${progress}%` }}
                        ></div>
                    </div>
                    <p className="text-sm text-gray-600">
                        Question {currentQuestion + 1} sur {exercise.content.questions.length}
                    </p>
                </div>

                {/* Question actuelle */}
                <div className="bg-white rounded-lg shadow-md p-8 mb-6">
                    <h2 className="text-xl font-semibold text-gray-800 mb-6">
                        {currentQ.question}
                    </h2>
                    
                    <div className="space-y-3">
                        {currentQ.options.map((option, index) => (
                            <button
                                key={index}
                                onClick={() => handleAnswerSelect(currentQuestion, index)}
                                className={`w-full text-left p-4 rounded-lg border-2 transition-all ${
                                    answers[currentQuestion] === index
                                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                                }`}
                            >
                                <div className="flex items-center space-x-3">
                                    <div className={`w-4 h-4 rounded-full border-2 ${
                                        answers[currentQuestion] === index
                                            ? 'border-blue-500 bg-blue-500'
                                            : 'border-gray-300'
                                    }`}>
                                        {answers[currentQuestion] === index && (
                                            <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                                        )}
                                    </div>
                                    <span className="font-medium">{String.fromCharCode(65 + index)}.</span>
                                    <span>{option}</span>
                                </div>
                            </button>
                        ))}
                    </div>
                </div>

                {/* Navigation */}
                <div className="flex justify-between items-center">
                    <button
                        onClick={handlePrevious}
                        disabled={currentQuestion === 0}
                        className={`px-6 py-2 rounded-md font-medium transition-colors ${
                            currentQuestion === 0
                                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                : 'bg-gray-600 text-white hover:bg-gray-700'
                        }`}
                    >
                        ← Précédent
                    </button>

                    <div className="flex space-x-2">
                        {exercise.content.questions.map((_, index) => (
                            <button
                                key={index}
                                onClick={() => setCurrentQuestion(index)}
                                className={`w-8 h-8 rounded-full text-sm font-medium transition-colors ${
                                    index === currentQuestion
                                        ? 'bg-blue-600 text-white'
                                        : answers.hasOwnProperty(index)
                                        ? 'bg-green-100 text-green-700 border border-green-300'
                                        : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                                }`}
                            >
                                {index + 1}
                            </button>
                        ))}
                    </div>

                    {currentQuestion === exercise.content.questions.length - 1 ? (
                        <button
                            onClick={handleSubmit}
                            disabled={!allAnswered || submitting}
                            className={`px-6 py-2 rounded-md font-medium transition-colors ${
                                !allAnswered || submitting
                                    ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                    : 'bg-green-600 text-white hover:bg-green-700'
                            }`}
                        >
                            {submitting ? 'Envoi...' : 'Terminer'}
                        </button>
                    ) : (
                        <button
                            onClick={handleNext}
                            disabled={currentQuestion === exercise.content.questions.length - 1}
                            className={`px-6 py-2 rounded-md font-medium transition-colors ${
                                currentQuestion === exercise.content.questions.length - 1
                                    ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                    : 'bg-blue-600 text-white hover:bg-blue-700'
                            }`}
                        >
                            Suivant →
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
}

export default ExerciseDetail;
