import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { courseService, registrationService } from '../../services/api';

function CourseDetails() {
    const { id } = useParams();
    const navigate = useNavigate();
    const [course, setCourse] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [registering, setRegistering] = useState(false);
    const [registrationSuccess, setRegistrationSuccess] = useState(false);
    const [registrationError, setRegistrationError] = useState(null);
    const [formData, setFormData] = useState({
        student_name: '',
        student_email: '',
        student_phone: '',
        motivation: ''
    });

    useEffect(() => {
        loadCourse();
    }, [id]);

    const loadCourse = async () => {
        try {
            setLoading(true);
            const response = await courseService.getCourse(id);
            if (response.success) {
                setCourse(response.data);
            }
        } catch (err) {
            setError('Erreur lors du chargement du cours');
            console.error('Erreur:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setRegistering(true);
        setRegistrationError(null);

        try {
            const response = await registrationService.registerToCourse(id, formData);
            if (response.success) {
                setRegistrationSuccess(true);
                setFormData({
                    student_name: '',
                    student_email: '',
                    student_phone: '',
                    motivation: ''
                });
                // Recharger les données du cours pour mettre à jour les places disponibles
                loadCourse();
            }
        } catch (err) {
            if (err.response && err.response.data && err.response.data.message) {
                setRegistrationError(err.response.data.message);
            } else {
                setRegistrationError('Erreur lors de l\'inscription. Veuillez réessayer.');
            }
            console.error('Erreur d\'inscription:', err);
        } finally {
            setRegistering(false);
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Chargement du cours...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <p className="text-red-600 mb-4">{error}</p>
                    <button
                        onClick={() => navigate('/')}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                    >
                        Retour à l'accueil
                    </button>
                </div>
            </div>
        );
    }

    if (!course) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <p className="text-gray-600 mb-4">Cours non trouvé</p>
                    <button
                        onClick={() => navigate('/')}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                    >
                        Retour à l'accueil
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Bouton retour */}
                <button
                    onClick={() => navigate('/')}
                    className="mb-6 flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                    Retour aux cours
                </button>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Détails du cours */}
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <div className="flex justify-between items-start mb-4">
                            <h1 className="text-2xl font-bold text-gray-800">{course.title}</h1>
                            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                course.level === 'Débutant' ? 'bg-green-100 text-green-800' :
                                course.level === 'Intermédiaire' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                            }`}>
                                {course.level}
                            </span>
                        </div>

                        <div className="space-y-4 mb-6">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <h3 className="font-semibold text-gray-700">Langue</h3>
                                    <p className="text-gray-600">{course.language}</p>
                                </div>
                                <div>
                                    <h3 className="font-semibold text-gray-700">Format</h3>
                                    <p className="text-gray-600">{course.format}</p>
                                </div>
                                <div>
                                    <h3 className="font-semibold text-gray-700">Durée</h3>
                                    <p className="text-gray-600">{course.duration_weeks} semaines</p>
                                </div>
                                <div>
                                    <h3 className="font-semibold text-gray-700">Date de début</h3>
                                    <p className="text-gray-600">{course.start_date_formatted}</p>
                                </div>
                            </div>

                            <div>
                                <h3 className="font-semibold text-gray-700 mb-2">Places disponibles</h3>
                                <div className="flex items-center">
                                    <div className="flex-1 bg-gray-200 rounded-full h-2 mr-3">
                                        <div
                                            className={`h-2 rounded-full ${course.is_full ? 'bg-red-500' : 'bg-green-500'}`}
                                            style={{ width: `${((course.max_spots - course.remaining_spots) / course.max_spots) * 100}%` }}
                                        ></div>
                                    </div>
                                    <span className="text-sm text-gray-600">
                                        {course.remaining_spots}/{course.max_spots}
                                    </span>
                                </div>
                                {course.is_full && (
                                    <p className="text-red-600 text-sm mt-1">Ce cours est complet</p>
                                )}
                            </div>


                        </div>

                        <div>
                            <h3 className="font-semibold text-gray-700 mb-2">Description</h3>
                            <p className="text-gray-600 leading-relaxed">{course.description}</p>
                        </div>
                    </div>

                    {/* Formulaire d'inscription */}
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <h2 className="text-xl font-bold text-gray-800 mb-6">S'inscrire à ce cours</h2>

                        {registrationSuccess && (
                            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                                <p className="font-semibold">Inscription réussie !</p>
                                <p className="text-sm">Vous recevrez une confirmation par email.</p>
                            </div>
                        )}

                        {registrationError && (
                            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                                <p>{registrationError}</p>
                            </div>
                        )}

                        {course.is_full ? (
                            <div className="text-center py-8">
                                <p className="text-gray-600 mb-4">Ce cours est complet.</p>
                                <p className="text-sm text-gray-500">
                                    Consultez nos autres cours disponibles.
                                </p>
                            </div>
                        ) : (
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div>
                                    <label htmlFor="student_name" className="block text-sm font-medium text-gray-700 mb-1">
                                        Nom complet *
                                    </label>
                                    <input
                                        type="text"
                                        id="student_name"
                                        name="student_name"
                                        value={formData.student_name}
                                        onChange={handleInputChange}
                                        required
                                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Votre nom complet"
                                    />
                                </div>

                                <div>
                                    <label htmlFor="student_email" className="block text-sm font-medium text-gray-700 mb-1">
                                        Email *
                                    </label>
                                    <input
                                        type="email"
                                        id="student_email"
                                        name="student_email"
                                        value={formData.student_email}
                                        onChange={handleInputChange}
                                        required
                                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="<EMAIL>"
                                    />
                                </div>

                                <div>
                                    <label htmlFor="student_phone" className="block text-sm font-medium text-gray-700 mb-1">
                                        Téléphone
                                    </label>
                                    <input
                                        type="tel"
                                        id="student_phone"
                                        name="student_phone"
                                        value={formData.student_phone}
                                        onChange={handleInputChange}
                                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Votre numéro de téléphone"
                                    />
                                </div>

                                <div>
                                    <label htmlFor="motivation" className="block text-sm font-medium text-gray-700 mb-1">
                                        Motivation (optionnel)
                                    </label>
                                    <textarea
                                        id="motivation"
                                        name="motivation"
                                        value={formData.motivation}
                                        onChange={handleInputChange}
                                        rows={4}
                                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Pourquoi souhaitez-vous suivre ce cours ?"
                                    />
                                </div>

                                <button
                                    type="submit"
                                    disabled={registering}
                                    className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
                                        registering
                                            ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                                            : 'bg-blue-600 text-white hover:bg-blue-700'
                                    }`}
                                >
                                    {registering ? 'Inscription en cours...' : 'S\'inscrire'}
                                </button>
                            </form>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default CourseDetails;
