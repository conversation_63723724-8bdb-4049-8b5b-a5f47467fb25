<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Registration extends Model
{
    protected $fillable = [
        'course_id',
        'student_name',
        'student_email',
        'student_phone',
        'motivation',
        'status',
        'registered_at'
    ];

    protected $casts = [
        'registered_at' => 'datetime'
    ];

    /**
     * Relation avec le cours
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Scope pour les inscriptions confirmées
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'Confirmée');
    }

    /**
     * Scope pour les inscriptions en attente
     */
    public function scopePending($query)
    {
        return $query->where('status', 'En attente');
    }

    /**
     * Scope pour les inscriptions annulées
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'Annulée');
    }
}
