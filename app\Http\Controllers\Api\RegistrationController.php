<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Registration;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class RegistrationController extends Controller
{
    /**
     * Display a listing of the resource (pour l'admin).
     */
    public function index(Request $request): JsonResponse
    {
        $query = Registration::with('course');

        // Filtres
        if ($request->has('course_id') && $request->course_id) {
            $query->where('course_id', $request->course_id);
        }

        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('language') && $request->language) {
            $query->whereHas('course', function ($q) use ($request) {
                $q->where('language', $request->language);
            });
        }

        // Tri
        $sortBy = $request->get('sort_by', 'registered_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $allowedSorts = ['registered_at', 'student_name', 'student_email', 'status'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $registrations = $query->get()->map(function ($registration) {
            return [
                'id' => $registration->id,
                'student_name' => $registration->student_name,
                'student_email' => $registration->student_email,
                'student_phone' => $registration->student_phone,
                'motivation' => $registration->motivation,
                'status' => $registration->status,
                'registered_at' => $registration->registered_at->format('Y-m-d H:i:s'),
                'registered_at_formatted' => $registration->registered_at->format('d/m/Y à H:i'),
                'course' => [
                    'id' => $registration->course->id,
                    'title' => $registration->course->title,
                    'language' => $registration->course->language,
                    'level' => $registration->course->level,
                    'start_date' => $registration->course->start_date->format('d/m/Y')
                ]
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $registrations
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, Course $course): JsonResponse
    {
        // Vérifier si le cours est actif
        if (!$course->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Ce cours n\'est plus disponible pour les inscriptions'
            ], 422);
        }

        // Vérifier s'il reste des places
        if ($course->isFull()) {
            return response()->json([
                'success' => false,
                'message' => 'Ce cours est complet, aucune place disponible'
            ], 422);
        }

        $validated = $request->validate([
            'student_name' => 'required|string|max:255',
            'student_email' => 'required|email|max:255',
            'student_phone' => 'nullable|string|max:20',
            'motivation' => 'nullable|string|max:1000'
        ]);

        // Vérifier si l'étudiant n'est pas déjà inscrit à ce cours
        $existingRegistration = Registration::where('course_id', $course->id)
            ->where('student_email', $validated['student_email'])
            ->whereIn('status', ['En attente', 'Confirmée'])
            ->first();

        if ($existingRegistration) {
            return response()->json([
                'success' => false,
                'message' => 'Vous êtes déjà inscrit(e) à ce cours'
            ], 422);
        }

        $validated['course_id'] = $course->id;
        $validated['status'] = 'En attente';

        $registration = Registration::create($validated);

        // Décrémenter le nombre de places disponibles
        $course->decrement('available_spots');

        return response()->json([
            'success' => true,
            'message' => 'Inscription enregistrée avec succès ! Vous recevrez une confirmation par email.',
            'data' => [
                'id' => $registration->id,
                'student_name' => $registration->student_name,
                'student_email' => $registration->student_email,
                'status' => $registration->status,
                'registered_at' => $registration->registered_at->format('d/m/Y à H:i'),
                'course' => [
                    'title' => $course->title,
                    'language' => $course->language,
                    'level' => $course->level,
                    'start_date' => $course->start_date->format('d/m/Y')
                ]
            ]
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Registration $registration): JsonResponse
    {
        $registration->load('course');

        $data = [
            'id' => $registration->id,
            'student_name' => $registration->student_name,
            'student_email' => $registration->student_email,
            'student_phone' => $registration->student_phone,
            'motivation' => $registration->motivation,
            'status' => $registration->status,
            'registered_at' => $registration->registered_at->format('Y-m-d H:i:s'),
            'registered_at_formatted' => $registration->registered_at->format('d/m/Y à H:i'),
            'course' => [
                'id' => $registration->course->id,
                'title' => $registration->course->title,
                'language' => $registration->course->language,
                'level' => $registration->course->level,
                'start_date' => $registration->course->start_date->format('d/m/Y'),
                'format' => $registration->course->format,
                'duration_weeks' => $registration->course->duration_weeks
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Update the status of a registration (admin only).
     */
    public function updateStatus(Request $request, Registration $registration): JsonResponse
    {
        $validated = $request->validate([
            'status' => ['required', Rule::in(['En attente', 'Confirmée', 'Annulée'])]
        ]);

        $oldStatus = $registration->status;
        $newStatus = $validated['status'];

        $registration->update(['status' => $newStatus]);

        // Ajuster le nombre de places disponibles si nécessaire
        if ($oldStatus === 'Confirmée' && $newStatus !== 'Confirmée') {
            // Libérer une place
            $registration->course->increment('available_spots');
        } elseif ($oldStatus !== 'Confirmée' && $newStatus === 'Confirmée') {
            // Occuper une place
            $registration->course->decrement('available_spots');
        }

        return response()->json([
            'success' => true,
            'message' => 'Statut de l\'inscription mis à jour avec succès',
            'data' => $registration->fresh()
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Registration $registration): JsonResponse
    {
        // Si l'inscription était confirmée, libérer la place
        if ($registration->status === 'Confirmée') {
            $registration->course->increment('available_spots');
        }

        $registration->delete();

        return response()->json([
            'success' => true,
            'message' => 'Inscription supprimée avec succès'
        ]);
    }
}
