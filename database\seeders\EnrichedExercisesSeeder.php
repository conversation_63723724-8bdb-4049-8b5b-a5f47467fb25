<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Exercise;
use App\Models\Lesson;
use App\Models\ExerciseType;

class EnrichedExercisesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer les leçons et types d'exercices
        $lessons = Lesson::all()->keyBy(fn($lesson) => $lesson->language . '_' . $lesson->level . '_' . $lesson->order);
        $exerciseTypes = ExerciseType::all()->keyBy('name');

        $exercises = [
            // ANGLAIS - Salutations
            [
                'title' => 'Traduire "Bonjour"',
                'question' => 'Comment dit-on "Bonjour" en anglais ?',
                'language' => 'en',
                'difficulty' => 1,
                'points' => 10,
                'lesson_id' => $lessons['en_1_1']->id ?? null,
                'exercise_type_id' => $exerciseTypes['multiple_choice']->id ?? null,
                'exercise_type' => 'multiple_choice',
                'content' => [
                    'question' => 'Comment dit-on "Bonjour" en anglais ?',
                    'options' => ['Hello', 'Goodbye', 'Thank you', 'Please'],
                    'correct_answer' => 'Hello',
                    'explanation' => '"Hello" est la salutation la plus courante en anglais.'
                ],
                'instruction' => 'Choisissez la bonne traduction',
                'hint' => 'C\'est la salutation la plus commune',
                'has_audio' => true,
                'has_image' => false
            ],
            [
                'title' => 'Compléter la salutation',
                'question' => 'Good _____, how are you?',
                'language' => 'en',
                'difficulty' => 1,
                'points' => 15,
                'lesson_id' => $lessons['en_1_1']->id ?? null,
                'exercise_type_id' => $exerciseTypes['fill_blank']->id ?? null,
                'exercise_type' => 'fill_blank',
                'content' => [
                    'sentence' => 'Good _____, how are you?',
                    'options' => ['morning', 'evening', 'afternoon'],
                    'correct_answer' => 'morning',
                    'explanation' => '"Good morning" est utilisé le matin.'
                ],
                'instruction' => 'Complétez la phrase avec le bon mot',
                'hint' => 'Pensez au moment de la journée'
            ],

            // ANGLAIS - Nombres
            [
                'title' => 'Associer nombres et mots',
                'question' => 'Associez les nombres avec leur écriture en lettres',
                'language' => 'en',
                'difficulty' => 1,
                'points' => 20,
                'lesson_id' => $lessons['en_1_2']->id ?? null,
                'exercise_type_id' => $exerciseTypes['matching']->id ?? null,
                'exercise_type' => 'matching',
                'content' => [
                    'pairs' => [
                        ['left' => '1', 'right' => 'One'],
                        ['left' => '2', 'right' => 'Two'],
                        ['left' => '3', 'right' => 'Three'],
                        ['left' => '4', 'right' => 'Four'],
                        ['left' => '5', 'right' => 'Five']
                    ]
                ],
                'instruction' => 'Associez chaque chiffre avec son mot correspondant'
            ],

            // ESPAGNOL - Salutations
            [
                'title' => 'Traduire "Hola"',
                'question' => 'Que signifie "Hola" en français ?',
                'language' => 'es',
                'difficulty' => 1,
                'points' => 10,
                'lesson_id' => $lessons['es_1_1']->id ?? null,
                'exercise_type_id' => $exerciseTypes['multiple_choice']->id ?? null,
                'exercise_type' => 'multiple_choice',
                'content' => [
                    'question' => 'Que signifie "Hola" en français ?',
                    'options' => ['Bonjour', 'Au revoir', 'Merci', 'S\'il vous plaît'],
                    'correct_answer' => 'Bonjour',
                    'explanation' => '"Hola" est la salutation de base en espagnol.'
                ],
                'instruction' => 'Choisissez la bonne traduction',
                'has_audio' => true
            ],
            [
                'title' => 'Écouter et écrire',
                'question' => 'Écoutez et écrivez ce que vous entendez',
                'language' => 'es',
                'difficulty' => 2,
                'points' => 25,
                'lesson_id' => $lessons['es_1_1']->id ?? null,
                'exercise_type_id' => $exerciseTypes['listening']->id ?? null,
                'exercise_type' => 'listening',
                'content' => [
                    'audio_text' => 'Buenos días',
                    'correct_answer' => 'Buenos días',
                    'alternatives' => ['Buenos dias', 'Buenas días'],
                    'audio_url' => '/audio/es/buenos_dias.mp3'
                ],
                'instruction' => 'Écoutez attentivement et tapez ce que vous entendez',
                'has_audio' => true,
                'time_limit' => 60
            ],

            // FRANÇAIS - Présentations
            [
                'title' => 'Se présenter',
                'question' => 'Comment dit-on "My name is" en français ?',
                'language' => 'fr',
                'difficulty' => 1,
                'points' => 10,
                'lesson_id' => $lessons['fr_1_1']->id ?? null,
                'exercise_type_id' => $exerciseTypes['translation']->id ?? null,
                'exercise_type' => 'translation',
                'content' => [
                    'text_to_translate' => 'My name is',
                    'correct_answer' => 'Je m\'appelle',
                    'alternatives' => ['Je m\'appelle', 'Je mappelle', 'Je me appelle'],
                    'explanation' => 'En français, on utilise "Je m\'appelle" pour dire son nom.'
                ],
                'instruction' => 'Traduisez cette expression en français',
                'hint' => 'Utilisez le verbe "s\'appeler"'
            ]
        ];

        foreach ($exercises as $exercise) {
            Exercise::create($exercise);
        }
    }
}
