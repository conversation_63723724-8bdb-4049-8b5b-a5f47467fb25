<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class CourseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Course::with('registrations')->active();

        // Filtres
        if ($request->has('language') && $request->language) {
            $query->byLanguage($request->language);
        }

        if ($request->has('level') && $request->level) {
            $query->byLevel($request->level);
        }

        // Tri
        $sortBy = $request->get('sort_by', 'start_date');
        $sortOrder = $request->get('sort_order', 'asc');

        $allowedSorts = ['start_date', 'language', 'level', 'duration_weeks', 'price'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $courses = $query->get()->map(function ($course) {
            return [
                'id' => $course->id,
                'language' => $course->language,
                'level' => $course->level,
                'title' => $course->title,
                'description' => $course->description,
                'duration_weeks' => $course->duration_weeks,
                'available_spots' => $course->available_spots,
                'remaining_spots' => $course->getRemainingSpots(),
                'max_spots' => $course->max_spots,
                'start_date' => $course->start_date ? $course->start_date->format('Y-m-d') : null,
                'start_date_formatted' => $course->start_date ? $course->start_date->format('d/m/Y') : 'À définir',
                'format' => $course->format,
                'price' => $course->price,
                'is_full' => $course->isFull(),
                'registrations_count' => $course->confirmedRegistrations()->count()
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $courses
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'language' => 'required|string|max:255',
            'level' => ['required', Rule::in(['Débutant', 'Intermédiaire', 'Avancé'])],
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'duration_weeks' => 'required|integer|min:1|max:52',
            'available_spots' => 'required|integer|min:1',
            'max_spots' => 'required|integer|min:1',
            'start_date' => 'required|date|after:today',
            'format' => ['required', Rule::in(['Visioconférence', 'Présentiel', 'Hybride'])],
            'price' => 'nullable|numeric|min:0',
            'is_active' => 'boolean'
        ]);

        // S'assurer que available_spots <= max_spots
        if ($validated['available_spots'] > $validated['max_spots']) {
            $validated['available_spots'] = $validated['max_spots'];
        }

        $course = Course::create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Cours créé avec succès',
            'data' => $course
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Course $course): JsonResponse
    {
        $course->load('registrations');

        $courseData = [
            'id' => $course->id,
            'language' => $course->language,
            'level' => $course->level,
            'title' => $course->title,
            'description' => $course->description,
            'duration_weeks' => $course->duration_weeks,
            'available_spots' => $course->available_spots,
            'remaining_spots' => $course->getRemainingSpots(),
            'max_spots' => $course->max_spots,
            'start_date' => $course->start_date ? $course->start_date->format('Y-m-d') : null,
            'start_date_formatted' => $course->start_date ? $course->start_date->format('d/m/Y') : 'À définir',
            'format' => $course->format,
            'price' => $course->price,
            'is_active' => $course->is_active,
            'is_full' => $course->isFull(),
            'registrations_count' => $course->confirmedRegistrations()->count(),
            'created_at' => $course->created_at,
            'updated_at' => $course->updated_at
        ];

        return response()->json([
            'success' => true,
            'data' => $courseData
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Course $course): JsonResponse
    {
        $validated = $request->validate([
            'language' => 'sometimes|string|max:255',
            'level' => ['sometimes', Rule::in(['Débutant', 'Intermédiaire', 'Avancé'])],
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'duration_weeks' => 'sometimes|integer|min:1|max:52',
            'available_spots' => 'sometimes|integer|min:0',
            'max_spots' => 'sometimes|integer|min:1',
            'start_date' => 'sometimes|date',
            'format' => ['sometimes', Rule::in(['Visioconférence', 'Présentiel', 'Hybride'])],
            'price' => 'sometimes|nullable|numeric|min:0',
            'is_active' => 'sometimes|boolean'
        ]);

        // Vérifications de cohérence
        if (isset($validated['available_spots']) && isset($validated['max_spots'])) {
            if ($validated['available_spots'] > $validated['max_spots']) {
                $validated['available_spots'] = $validated['max_spots'];
            }
        } elseif (isset($validated['available_spots'])) {
            if ($validated['available_spots'] > $course->max_spots) {
                $validated['available_spots'] = $course->max_spots;
            }
        } elseif (isset($validated['max_spots'])) {
            if ($course->available_spots > $validated['max_spots']) {
                $validated['available_spots'] = $validated['max_spots'];
            }
        }

        $course->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Cours mis à jour avec succès',
            'data' => $course->fresh()
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Course $course): JsonResponse
    {
        // Vérifier s'il y a des inscriptions confirmées
        if ($course->confirmedRegistrations()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Impossible de supprimer un cours avec des inscriptions confirmées'
            ], 422);
        }

        $course->delete();

        return response()->json([
            'success' => true,
            'message' => 'Cours supprimé avec succès'
        ]);
    }
}
