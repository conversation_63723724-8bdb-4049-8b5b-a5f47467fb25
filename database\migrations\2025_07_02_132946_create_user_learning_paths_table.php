<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_learning_paths', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->default(1);
            $table->unsignedBigInteger('learning_path_id');
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->json('completed_lessons')->nullable(); // IDs des leçons terminées
            $table->json('completed_exercises')->nullable(); // IDs des exercices terminés
            $table->integer('current_lesson_index')->default(0);
            $table->integer('current_exercise_index')->default(0);
            $table->float('completion_percentage')->default(0);
            $table->integer('total_xp_earned')->default(0);
            $table->json('performance_data')->nullable(); // Données de performance
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->foreign('learning_path_id')->references('id')->on('learning_paths')->onDelete('cascade');
            $table->unique(['user_id', 'learning_path_id']);
            $table->index(['user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_learning_paths');
    }
};
