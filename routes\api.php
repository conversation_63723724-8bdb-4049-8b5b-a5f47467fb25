<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\CourseController;
use App\Http\Controllers\Api\RegistrationController;
use App\Http\Controllers\Api\ExerciseController;
use App\Http\Controllers\Api\LessonController;
use App\Http\Controllers\Api\ProgressController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Routes publiques pour les cours
Route::apiResource('courses', CourseController::class)->only(['index', 'show']);

// Routes pour les inscriptions
Route::post('courses/{course}/register', [RegistrationController::class, 'store']);
Route::get('registrations/{registration}', [RegistrationController::class, 'show']);

// Routes pour les exercices
Route::apiResource('exercises', ExerciseController::class)->only(['index', 'show']);
Route::get('exercises/language/{language}', [ExerciseController::class, 'getInteractiveByLanguage']);
Route::post('exercises/{exercise}/submit', [ExerciseController::class, 'submitAnswers']);

// Routes pour les leçons
Route::apiResource('lessons', LessonController::class)->only(['index', 'show']);
Route::get('lessons/language/{language}', [LessonController::class, 'getByLanguage']);
Route::post('lessons/{lesson}/complete', [LessonController::class, 'complete']);

// Routes pour la progression
Route::get('progress/{language}', [ProgressController::class, 'getProgress']);
Route::get('progress', [ProgressController::class, 'getAllProgress']);
Route::get('achievements/{language}', [ProgressController::class, 'getAchievements']);



// Routes d'administration (pour plus tard, on peut ajouter de l'authentification)
Route::prefix('admin')->group(function () {
    Route::apiResource('courses', CourseController::class)->except(['index', 'show']);
    Route::get('registrations', [RegistrationController::class, 'index']);
    Route::patch('registrations/{registration}/status', [RegistrationController::class, 'updateStatus']);
    Route::delete('registrations/{registration}', [RegistrationController::class, 'destroy']);
});

// Routes utilitaires
Route::get('languages', function () {
    return response()->json([
        'success' => true,
        'data' => ['Anglais', 'Espagnol', 'Italien', 'Français', 'Allemand', 'Portugais', 'Chinois', 'Japonais']
    ]);
});

Route::get('levels', function () {
    return response()->json([
        'success' => true,
        'data' => ['Débutant', 'Intermédiaire', 'Avancé']
    ]);
});

Route::get('formats', function () {
    return response()->json([
        'success' => true,
        'data' => ['Visioconférence', 'Présentiel', 'Hybride']
    ]);
});
