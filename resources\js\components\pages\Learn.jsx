import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

const Learn = () => {
    const navigate = useNavigate();
    const { language } = useParams();
    const [selectedLanguage, setSelectedLanguage] = useState(language || '');

    const languages = [
        { code: 'en', name: '<PERSON><PERSON><PERSON>', flag: '🇬🇧', color: 'from-blue-500 to-blue-600', students: '2.1M', description: 'La langue internationale' },
        { code: 'es', name: 'Espagno<PERSON>', flag: '🇪🇸', color: 'from-red-500 to-red-600', students: '1.8M', description: 'Parlé dans 21 pays' },
        { code: 'fr', name: 'Français', flag: '🇫🇷', color: 'from-blue-400 to-indigo-600', students: '1.2M', description: 'La langue de l\'amour' },
        { code: 'de', name: 'Allemand', flag: '🇩🇪', color: 'from-yellow-500 to-red-600', students: '950K', description: '<PERSON>ue de l\'innovation' },
        { code: 'it', name: 'Italien', flag: '🇮🇹', color: 'from-green-500 to-red-600', students: '780K', description: 'Langue de l\'art et culture' },
        { code: 'pt', name: 'Portugais', flag: '🇵🇹', color: 'from-green-500 to-red-500', students: '650K', description: 'Parlé au Brésil et Portugal' },
        { code: 'zh', name: 'Chinois', flag: '🇨🇳', color: 'from-red-600 to-yellow-500', students: '1.5M', description: 'La langue la plus parlée' },
        { code: 'ja', name: 'Japonais', flag: '🇯🇵', color: 'from-red-500 to-white', students: '890K', description: 'Langue de la technologie' }
    ];

    const startLearning = (languageCode) => {
        navigate(`/learn/${languageCode}/lessons`);
    };

    const goToExercises = (languageCode) => {
        navigate(`/exercises?language=${languageCode}`);
    };

    // Si une langue spécifique est sélectionnée, afficher le dashboard de cette langue
    if (language) {
        const currentLang = languages.find(l => l.code === language);
        if (!currentLang) {
            return (
                <div className="min-h-screen bg-gradient-to-br from-red-400 to-red-600 flex items-center justify-center">
                    <div className="text-center text-white">
                        <h1 className="text-4xl font-bold mb-4">Langue non trouvée</h1>
                        <button 
                            onClick={() => navigate('/learn')}
                            className="bg-white text-red-600 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-all"
                        >
                            Retour à la sélection
                        </button>
                    </div>
                </div>
            );
        }

        return (
            <div className={`min-h-screen bg-gradient-to-br ${currentLang.color}`}>
                <div className="max-w-6xl mx-auto px-4 py-8">
                    {/* Header avec langue sélectionnée */}
                    <div className="text-center mb-12">
                        <div className="text-8xl mb-4 animate-bounce">
                            {currentLang.flag}
                        </div>
                        <h1 className="text-5xl font-bold text-white mb-4">
                            Apprendre l'{currentLang.name}
                        </h1>
                        <p className="text-xl text-white/80 mb-8">
                            {currentLang.description} • {currentLang.students} apprenants
                        </p>
                    </div>

                    {/* Options d'apprentissage */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {/* Leçons */}
                        <div 
                            onClick={() => startLearning(language)}
                            className="bg-white/20 backdrop-blur-sm rounded-2xl p-8 cursor-pointer transform hover:scale-105 transition-all duration-300 hover:bg-white/30"
                        >
                            <div className="text-center">
                                <div className="text-6xl mb-4">📚</div>
                                <h3 className="text-2xl font-bold text-white mb-4">Leçons</h3>
                                <p className="text-white/80 mb-6">
                                    Apprenez avec des leçons structurées et progressives
                                </p>
                                <div className="bg-white text-gray-800 px-6 py-3 rounded-full font-semibold">
                                    Commencer les leçons
                                </div>
                            </div>
                        </div>

                        {/* Exercices */}
                        <div 
                            onClick={() => goToExercises(language)}
                            className="bg-white/20 backdrop-blur-sm rounded-2xl p-8 cursor-pointer transform hover:scale-105 transition-all duration-300 hover:bg-white/30"
                        >
                            <div className="text-center">
                                <div className="text-6xl mb-4">✏️</div>
                                <h3 className="text-2xl font-bold text-white mb-4">Exercices</h3>
                                <p className="text-white/80 mb-6">
                                    Pratiquez avec des exercices interactifs variés
                                </p>
                                <div className="bg-white text-gray-800 px-6 py-3 rounded-full font-semibold">
                                    Faire des exercices
                                </div>
                            </div>
                        </div>

                        {/* Progression */}
                        <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-8">
                            <div className="text-center">
                                <div className="text-6xl mb-4">🏆</div>
                                <h3 className="text-2xl font-bold text-white mb-4">Progression</h3>
                                <div className="space-y-4">
                                    <div className="bg-white/20 rounded-full p-4">
                                        <div className="text-white font-semibold">Niveau: Débutant</div>
                                        <div className="bg-white/30 rounded-full h-2 mt-2">
                                            <div className="bg-white rounded-full h-2 w-1/4"></div>
                                        </div>
                                    </div>
                                    <div className="text-white/80">
                                        <div>XP: 0 / 100</div>
                                        <div>Streak: 0 jours</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Bouton retour */}
                    <div className="text-center mt-12">
                        <button 
                            onClick={() => navigate('/learn')}
                            className="bg-white/20 backdrop-blur-sm text-white px-8 py-4 rounded-full font-semibold hover:bg-white/30 transition-all"
                        >
                            ← Changer de langue
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    // Page de sélection de langue
    return (
        <div className="min-h-screen bg-gradient-to-br from-green-400 via-blue-500 to-purple-600">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
                <div className="text-center mb-16">
                    <h1 className="text-6xl md:text-8xl font-bold text-white mb-6">
                        🌍 Learnify
                    </h1>
                    <p className="text-3xl md:text-4xl text-white/90 mb-4 font-bold">
                        Apprenez une langue gratuitement
                    </p>
                    <p className="text-xl md:text-2xl text-white/80 mb-12 max-w-3xl mx-auto">
                        Rejoignez des millions d'apprenants dans le monde entier. 
                        Choisissez votre langue et commencez votre aventure !
                    </p>
                </div>

                {/* Grille de sélection des langues */}
                <div className="max-w-6xl mx-auto">
                    <h2 className="text-4xl font-bold text-white text-center mb-12">
                        Choisissez votre langue
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {languages.map((lang) => (
                            <div
                                key={lang.code}
                                onClick={() => startLearning(lang.code)}
                                className={`bg-gradient-to-br ${lang.color} rounded-3xl p-8 cursor-pointer transform hover:scale-110 transition-all duration-300 shadow-2xl hover:shadow-3xl group relative overflow-hidden`}
                            >
                                {/* Effet de brillance */}
                                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                                
                                <div className="text-center relative z-10">
                                    <div className="text-7xl mb-6 group-hover:animate-bounce">
                                        {lang.flag}
                                    </div>
                                    <h3 className="text-3xl font-bold text-white mb-3">
                                        {lang.name}
                                    </h3>
                                    <p className="text-white/90 text-lg mb-2">
                                        {lang.students} apprenants
                                    </p>
                                    <p className="text-white/70 text-sm mb-6">
                                        {lang.description}
                                    </p>
                                    <div className="bg-white/30 backdrop-blur-sm rounded-full px-6 py-3 text-white font-bold text-lg group-hover:bg-white/40 transition-all">
                                        Commencer →
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Section statistiques */}
                <div className="text-center mt-20">
                    <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 max-w-4xl mx-auto">
                        <h3 className="text-3xl font-bold text-white mb-8">Pourquoi choisir Learnify ?</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <div className="text-center">
                                <div className="text-5xl mb-4">🎯</div>
                                <div className="text-2xl font-bold text-white mb-2">100% Gratuit</div>
                                <div className="text-white/80">Apprenez sans limites</div>
                            </div>
                            <div className="text-center">
                                <div className="text-5xl mb-4">⚡</div>
                                <div className="text-2xl font-bold text-white mb-2">Rapide & Efficace</div>
                                <div className="text-white/80">15 min par jour suffisent</div>
                            </div>
                            <div className="text-center">
                                <div className="text-5xl mb-4">🏆</div>
                                <div className="text-2xl font-bold text-white mb-2">Gamifié</div>
                                <div className="text-white/80">Apprenez en vous amusant</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Learn;
