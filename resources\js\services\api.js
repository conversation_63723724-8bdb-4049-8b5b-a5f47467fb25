import axios from 'axios';

// Configuration de base d'axios
const api = axios.create({
    baseURL: '/api',
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Intercepteur pour ajouter le token CSRF si nécessaire
api.interceptors.request.use((config) => {
    const token = document.querySelector('meta[name="csrf-token"]');
    if (token) {
        config.headers['X-CSRF-TOKEN'] = token.getAttribute('content');
    }
    return config;
});

// Données de fallback pour les tests
const fallbackCourses = [
    {
        id: 1,
        language: 'Anglais',
        level: 'Débutant',
        title: 'Anglais pour débutants',
        description: 'Apprenez les bases de l\'anglais : vocabulaire essentiel, grammaire de base, et expressions courantes pour la vie quotidienne.',
        duration_weeks: 8,
        available_spots: 12,
        remaining_spots: 12,
        max_spots: 12,
        start_date: '2025-07-15',
        start_date_formatted: '15/07/2025',
        format: 'Visioconférence',
        price: 0.00,
        is_full: false,
        registrations_count: 0
    },
    {
        id: 2,
        language: 'Espagnol',
        level: 'Intermédiaire',
        title: 'Espagnol intermédiaire',
        description: 'Perfectionnez votre espagnol avec des conversations avancées, la grammaire complexe et la culture hispanophone.',
        duration_weeks: 10,
        available_spots: 8,
        remaining_spots: 8,
        max_spots: 15,
        start_date: '2025-07-22',
        start_date_formatted: '22/07/2025',
        format: 'Hybride',
        price: 0.00,
        is_full: false,
        registrations_count: 7
    },
    {
        id: 3,
        language: 'Italien',
        level: 'Débutant',
        title: 'Italien pour débutants',
        description: 'Découvrez la belle langue italienne : prononciation, vocabulaire de base et culture italienne.',
        duration_weeks: 6,
        available_spots: 0,
        remaining_spots: 0,
        max_spots: 10,
        start_date: '2025-08-01',
        start_date_formatted: '01/08/2025',
        format: 'Présentiel',
        price: 0.00,
        is_full: true,
        registrations_count: 10
    }
];

// Service pour les cours
export const courseService = {
    // Récupérer tous les cours avec filtres optionnels
    async getCourses(filters = {}) {
        try {
            const params = new URLSearchParams();

            if (filters.language) params.append('language', filters.language);
            if (filters.level) params.append('level', filters.level);
            if (filters.sort_by) params.append('sort_by', filters.sort_by);
            if (filters.sort_order) params.append('sort_order', filters.sort_order);

            const response = await api.get(`/courses?${params.toString()}`);
            return response.data;
        } catch (error) {
            console.error('Erreur lors de la récupération des cours, utilisation des données de fallback:', error);
            // Retourner les données de fallback en cas d'erreur
            let filteredCourses = [...fallbackCourses];

            // Appliquer les filtres
            if (filters.language) {
                filteredCourses = filteredCourses.filter(course => course.language === filters.language);
            }
            if (filters.level) {
                filteredCourses = filteredCourses.filter(course => course.level === filters.level);
            }

            return { success: true, data: filteredCourses };
        }
    },

    // Récupérer un cours spécifique
    async getCourse(id) {
        try {
            const response = await api.get(`/courses/${id}`);
            return response.data;
        } catch (error) {
            console.error('Erreur lors de la récupération du cours, utilisation des données de fallback:', error);
            // Retourner les données de fallback en cas d'erreur
            const course = fallbackCourses.find(c => c.id === parseInt(id));
            if (course) {
                return { success: true, data: course };
            } else {
                throw error;
            }
        }
    },

    // Créer un nouveau cours (admin)
    async createCourse(courseData) {
        try {
            const response = await api.post('/admin/courses', courseData);
            return response.data;
        } catch (error) {
            console.error('Erreur lors de la création du cours:', error);
            throw error;
        }
    },

    // Mettre à jour un cours (admin)
    async updateCourse(id, courseData) {
        try {
            const response = await api.put(`/admin/courses/${id}`, courseData);
            return response.data;
        } catch (error) {
            console.error('Erreur lors de la mise à jour du cours:', error);
            throw error;
        }
    },

    // Supprimer un cours (admin)
    async deleteCourse(id) {
        try {
            const response = await api.delete(`/admin/courses/${id}`);
            return response.data;
        } catch (error) {
            console.error('Erreur lors de la suppression du cours:', error);
            throw error;
        }
    }
};

// Service pour les inscriptions
export const registrationService = {
    // S'inscrire à un cours
    async registerToCourse(courseId, registrationData) {
        try {
            const response = await api.post(`/courses/${courseId}/register`, registrationData);
            return response.data;
        } catch (error) {
            console.error('Erreur lors de l\'inscription:', error);
            throw error;
        }
    },

    // Récupérer une inscription
    async getRegistration(id) {
        try {
            const response = await api.get(`/registrations/${id}`);
            return response.data;
        } catch (error) {
            console.error('Erreur lors de la récupération de l\'inscription:', error);
            throw error;
        }
    },

    // Récupérer toutes les inscriptions (admin)
    async getRegistrations(filters = {}) {
        try {
            const params = new URLSearchParams();
            
            if (filters.course_id) params.append('course_id', filters.course_id);
            if (filters.status) params.append('status', filters.status);
            if (filters.language) params.append('language', filters.language);
            if (filters.sort_by) params.append('sort_by', filters.sort_by);
            if (filters.sort_order) params.append('sort_order', filters.sort_order);

            const response = await api.get(`/admin/registrations?${params.toString()}`);
            return response.data;
        } catch (error) {
            console.error('Erreur lors de la récupération des inscriptions:', error);
            throw error;
        }
    },

    // Mettre à jour le statut d'une inscription (admin)
    async updateRegistrationStatus(id, status) {
        try {
            const response = await api.patch(`/admin/registrations/${id}/status`, { status });
            return response.data;
        } catch (error) {
            console.error('Erreur lors de la mise à jour du statut:', error);
            throw error;
        }
    },

    // Supprimer une inscription (admin)
    async deleteRegistration(id) {
        try {
            const response = await api.delete(`/admin/registrations/${id}`);
            return response.data;
        } catch (error) {
            console.error('Erreur lors de la suppression de l\'inscription:', error);
            throw error;
        }
    }
};

// Service pour les données utilitaires
export const utilityService = {
    // Récupérer la liste des langues
    async getLanguages() {
        try {
            const response = await api.get('/languages');
            return response.data;
        } catch (error) {
            console.error('Erreur lors de la récupération des langues:', error);
            return { success: true, data: ['Anglais', 'Espagnol', 'Italien', 'Français', 'Allemand'] };
        }
    },

    // Récupérer la liste des niveaux
    async getLevels() {
        try {
            const response = await api.get('/levels');
            return response.data;
        } catch (error) {
            console.error('Erreur lors de la récupération des niveaux:', error);
            return { success: true, data: ['Débutant', 'Intermédiaire', 'Avancé'] };
        }
    },

    // Récupérer la liste des formats
    async getFormats() {
        try {
            const response = await api.get('/formats');
            return response.data;
        } catch (error) {
            console.error('Erreur lors de la récupération des formats:', error);
            return { success: true, data: ['Visioconférence', 'Présentiel', 'Hybride'] };
        }
    }
};

export default api;
