<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Lesson;
use App\Models\UserProgress;
use Illuminate\Http\JsonResponse;

class LessonController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $language = $request->get('language');
        $level = $request->get('level');

        $query = Lesson::active()->ordered();

        if ($language) {
            $query->byLanguage($language);
        }

        if ($level) {
            $query->byLevel($level);
        }

        $lessons = $query->get();

        return response()->json([
            'success' => true,
            'data' => $lessons,
            'message' => 'Leçons récupérées avec succès'
        ]);
    }

    public function show($id): JsonResponse
    {
        $lesson = Lesson::with('exercises')->find($id);

        if (!$lesson) {
            return response()->json([
                'success' => false,
                'message' => 'Leçon non trouvée'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $lesson,
            'message' => 'Leçon récupérée avec succès'
        ]);
    }

    public function getByLanguage($language): JsonResponse
    {
        $lessons = Lesson::active()
            ->byLanguage($language)
            ->ordered()
            ->get()
            ->groupBy('level');

        return response()->json([
            'success' => true,
            'data' => $lessons,
            'message' => "Leçons pour {$language} récupérées avec succès"
        ]);
    }

    public function complete(Request $request, $id): JsonResponse
    {
        $lesson = Lesson::find($id);

        if (!$lesson) {
            return response()->json([
                'success' => false,
                'message' => 'Leçon non trouvée'
            ], 404);
        }

        // Pour l'instant, on simule un utilisateur (ID 1)
        // Dans une vraie app, on utiliserait auth()->user()
        $userId = 1;

        $progress = UserProgress::firstOrCreate([
            'user_id' => $userId,
            'language' => $lesson->language
        ], [
            'current_level' => 1,
            'total_xp' => 0,
            'daily_xp' => 0,
            'streak_days' => 0,
            'completed_lessons' => [],
            'achievements' => []
        ]);

        $progress->completeLesson($lesson->id);
        $progress->addXp($lesson->xp_reward);

        return response()->json([
            'success' => true,
            'data' => [
                'lesson' => $lesson,
                'progress' => $progress,
                'xp_gained' => $lesson->xp_reward
            ],
            'message' => 'Leçon terminée avec succès !'
        ]);
    }
}
