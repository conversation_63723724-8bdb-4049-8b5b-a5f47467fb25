<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class UserLearningPath extends Model
{
    protected $fillable = [
        'user_id',
        'learning_path_id',
        'started_at',
        'completed_at',
        'completed_lessons',
        'completed_exercises',
        'current_lesson_index',
        'current_exercise_index',
        'completion_percentage',
        'total_xp_earned',
        'performance_data',
        'is_active'
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'completed_lessons' => 'array',
        'completed_exercises' => 'array',
        'performance_data' => 'array',
        'is_active' => 'boolean'
    ];

    // Relations
    public function learningPath()
    {
        return $this->belongsTo(LearningPath::class);
    }

    // Méthodes utilitaires
    public function markLessonCompleted($lessonId, $xpEarned = 0)
    {
        $completedLessons = $this->completed_lessons ?? [];

        if (!in_array($lessonId, $completedLessons)) {
            $completedLessons[] = $lessonId;
            $this->completed_lessons = $completedLessons;
            $this->current_lesson_index++;
            $this->total_xp_earned += $xpEarned;

            $this->updateCompletionPercentage();
            $this->save();
        }
    }

    public function markExerciseCompleted($exerciseId, $xpEarned = 0)
    {
        $completedExercises = $this->completed_exercises ?? [];

        if (!in_array($exerciseId, $completedExercises)) {
            $completedExercises[] = $exerciseId;
            $this->completed_exercises = $completedExercises;
            $this->current_exercise_index++;
            $this->total_xp_earned += $xpEarned;

            $this->updateCompletionPercentage();
            $this->save();
        }
    }

    public function updateCompletionPercentage()
    {
        $path = $this->learningPath;
        if (!$path) return;

        $totalSteps = $path->getTotalSteps();
        if ($totalSteps === 0) return;

        $completedSteps = count($this->completed_lessons ?? []) + count($this->completed_exercises ?? []);
        $this->completion_percentage = ($completedSteps / $totalSteps) * 100;

        // Marquer comme terminé si 100%
        if ($this->completion_percentage >= 100 && !$this->completed_at) {
            $this->completed_at = now();
            $this->total_xp_earned += $path->completion_reward_xp;
        }
    }

    public function getNextStep()
    {
        return $this->learningPath->getNextStepForUser($this->user_id);
    }

    public function getDaysActive()
    {
        if (!$this->started_at) return 0;

        $endDate = $this->completed_at ?? now();
        return $this->started_at->diffInDays($endDate);
    }

    public function getAverageSessionTime()
    {
        $performanceData = $this->performance_data ?? [];
        $sessions = $performanceData['sessions'] ?? [];

        if (empty($sessions)) return 0;

        $totalTime = array_sum(array_column($sessions, 'duration_minutes'));
        return $totalTime / count($sessions);
    }

    public function addPerformanceData($type, $data)
    {
        $performance = $this->performance_data ?? [];

        if (!isset($performance[$type])) {
            $performance[$type] = [];
        }

        $performance[$type][] = array_merge($data, [
            'timestamp' => now()->toISOString()
        ]);

        // Garder seulement les 50 dernières entrées par type
        $performance[$type] = array_slice($performance[$type], -50);

        $this->performance_data = $performance;
        $this->save();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCompleted($query)
    {
        return $query->whereNotNull('completed_at');
    }

    public function scopeInProgress($query)
    {
        return $query->whereNotNull('started_at')
                    ->whereNull('completed_at');
    }
}
