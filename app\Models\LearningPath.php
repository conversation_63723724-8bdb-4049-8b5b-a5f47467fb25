<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LearningPath extends Model
{
    protected $fillable = [
        'name',
        'language',
        'description',
        'level',
        'category',
        'lesson_sequence',
        'exercise_sequence',
        'estimated_duration_hours',
        'difficulty_progression',
        'prerequisites',
        'learning_objectives',
        'icon',
        'color',
        'is_active',
        'completion_reward_xp'
    ];

    protected $casts = [
        'lesson_sequence' => 'array',
        'exercise_sequence' => 'array',
        'prerequisites' => 'array',
        'learning_objectives' => 'array',
        'is_active' => 'boolean'
    ];

    // Relations
    public function userPaths()
    {
        return $this->hasMany(UserLearningPath::class);
    }

    public function lessons()
    {
        return $this->belongsToMany(Lesson::class, 'learning_path_lessons')
                    ->withPivot('order_index')
                    ->orderBy('pivot_order_index');
    }

    public function exercises()
    {
        return $this->belongsToMany(Exercise::class, 'learning_path_exercises')
                    ->withPivot('order_index')
                    ->orderBy('pivot_order_index');
    }

    // Scopes
    public function scopeByLanguage($query, $language)
    {
        return $query->where('language', $language);
    }

    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Méthodes utilitaires
    public function getTotalSteps()
    {
        return count($this->lesson_sequence ?? []) + count($this->exercise_sequence ?? []);
    }

    public function getProgressForUser($userId = 1)
    {
        $userPath = $this->userPaths()->where('user_id', $userId)->first();
        return $userPath ? $userPath->completion_percentage : 0;
    }

    public function isCompletedByUser($userId = 1)
    {
        $userPath = $this->userPaths()->where('user_id', $userId)->first();
        return $userPath && $userPath->completed_at !== null;
    }

    public function getNextStepForUser($userId = 1)
    {
        $userPath = $this->userPaths()->where('user_id', $userId)->first();

        if (!$userPath) {
            return [
                'type' => 'lesson',
                'index' => 0,
                'id' => $this->lesson_sequence[0] ?? null
            ];
        }

        // Vérifier les leçons
        if ($userPath->current_lesson_index < count($this->lesson_sequence ?? [])) {
            return [
                'type' => 'lesson',
                'index' => $userPath->current_lesson_index,
                'id' => $this->lesson_sequence[$userPath->current_lesson_index]
            ];
        }

        // Vérifier les exercices
        if ($userPath->current_exercise_index < count($this->exercise_sequence ?? [])) {
            return [
                'type' => 'exercise',
                'index' => $userPath->current_exercise_index,
                'id' => $this->exercise_sequence[$userPath->current_exercise_index]
            ];
        }

        return null; // Parcours terminé
    }
}
