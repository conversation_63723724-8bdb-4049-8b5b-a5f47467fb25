<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserExerciseProgress extends Model
{
    use HasFactory;

    protected $table = 'user_exercise_progress';

    protected $fillable = [
        'user_id',
        'exercise_id',
        'answers',
        'score',
        'max_score',
        'completed',
        'started_at',
        'completed_at'
    ];

    protected $casts = [
        'answers' => 'array',
        'completed' => 'boolean',
        'started_at' => 'datetime',
        'completed_at' => 'datetime'
    ];

    // Relation avec l'utilisateur
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Relation avec l'exercice
    public function exercise()
    {
        return $this->belongsTo(Exercise::class);
    }

    // Calculer le pourcentage de réussite
    public function getSuccessPercentageAttribute()
    {
        if ($this->max_score == 0) return 0;
        return round(($this->score / $this->max_score) * 100, 2);
    }
}
