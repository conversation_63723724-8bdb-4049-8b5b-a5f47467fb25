import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

const Review = () => {
    const { language } = useParams();
    const navigate = useNavigate();
    const [reviews, setReviews] = useState([]);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [userAnswer, setUserAnswer] = useState('');
    const [selectedOption, setSelectedOption] = useState('');
    const [showResult, setShowResult] = useState(false);
    const [isCorrect, setIsCorrect] = useState(false);
    const [score, setScore] = useState(0);
    const [loading, setLoading] = useState(true);
    const [stats, setStats] = useState({});
    const [sessionComplete, setSessionComplete] = useState(false);

    const languageNames = {
        'en': 'Anglais',
        'es': 'Espagnol', 
        'fr': 'Français',
        'de': 'Allemand',
        'it': 'Italien',
        'pt': 'Portugais',
        'zh': 'Chinois',
        'ja': 'Japonais'
    };

    const languageFlags = {
        'en': '🇬🇧',
        'es': '🇪🇸',
        'fr': '🇫🇷', 
        'de': '🇩🇪',
        'it': '🇮🇹',
        'pt': '🇵🇹',
        'zh': '🇨🇳',
        'ja': '🇯🇵'
    };

    useEffect(() => {
        fetchReviews();
        fetchStats();
    }, [language]);

    const fetchReviews = async () => {
        try {
            const response = await fetch(`/api/reviews/${language}/due`);
            const data = await response.json();
            if (data.success) {
                setReviews(data.data);
                if (data.data.length === 0) {
                    setSessionComplete(true);
                }
            }
        } catch (error) {
            console.error('Erreur lors du chargement des révisions:', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchStats = async () => {
        try {
            const response = await fetch(`/api/reviews/${language}/stats`);
            const data = await response.json();
            if (data.success) {
                setStats(data.data);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des statistiques:', error);
        }
    };

    const submitAnswer = async () => {
        if (!currentReview) return;

        let correct = false;
        let quality = 0;

        // Vérifier la réponse selon le type d'exercice
        switch (currentReview.exercise_type) {
            case 'multiple_choice':
                correct = selectedOption === currentReview.content.correct_answer;
                quality = correct ? 4 : 0;
                break;
            case 'translation':
            case 'fill_blank':
                const userAnswerLower = userAnswer.toLowerCase().trim();
                const correctAnswer = currentReview.content.correct_answer.toLowerCase();
                const alternatives = currentReview.content.alternatives || [];
                
                correct = userAnswerLower === correctAnswer || 
                         alternatives.some(alt => alt.toLowerCase() === userAnswerLower);
                quality = correct ? 4 : 0;
                break;
            default:
                correct = userAnswer.toLowerCase().trim() === currentReview.content.correct_answer.toLowerCase();
                quality = correct ? 4 : 0;
        }

        setIsCorrect(correct);
        setShowResult(true);

        if (correct) {
            setScore(score + currentReview.points);
        }

        // Soumettre à l'API
        try {
            await fetch(`/api/reviews/${currentReview.review_id}/submit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    is_correct: correct,
                    quality: quality
                })
            });
        } catch (error) {
            console.error('Erreur lors de la soumission:', error);
        }
    };

    const nextReview = () => {
        if (currentIndex < reviews.length - 1) {
            setCurrentIndex(currentIndex + 1);
            resetForm();
        } else {
            setSessionComplete(true);
        }
    };

    const resetForm = () => {
        setUserAnswer('');
        setSelectedOption('');
        setShowResult(false);
        setIsCorrect(false);
    };

    const currentReview = reviews[currentIndex];

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-pink-600 flex items-center justify-center">
                <div className="text-white text-2xl">Chargement des révisions...</div>
            </div>
        );
    }

    if (sessionComplete) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-pink-600 flex items-center justify-center p-4">
                <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 max-w-md w-full text-center">
                    <div className="text-6xl mb-4">🎉</div>
                    <h1 className="text-3xl font-bold text-white mb-4">Session terminée !</h1>
                    <div className="text-white/80 mb-6">
                        <p className="text-xl mb-2">Score total: {score} points</p>
                        <p>Exercices révisés: {reviews.length}</p>
                        <p>Taux de réussite: {reviews.length > 0 ? Math.round((score / (reviews.reduce((sum, r) => sum + r.points, 0))) * 100) : 0}%</p>
                    </div>
                    <div className="space-y-3">
                        <button
                            onClick={() => navigate(`/learn/${language}`)}
                            className="w-full bg-white/20 hover:bg-white/30 text-white font-bold py-3 px-6 rounded-xl transition-all duration-200"
                        >
                            Retour au dashboard
                        </button>
                        <button
                            onClick={() => window.location.reload()}
                            className="w-full bg-green-500/80 hover:bg-green-500 text-white font-bold py-3 px-6 rounded-xl transition-all duration-200"
                        >
                            Nouvelle session
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    if (!currentReview) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-pink-600 flex items-center justify-center">
                <div className="text-white text-2xl">Aucune révision disponible</div>
            </div>
        );
    }

    const renderExerciseContent = () => {
        switch (currentReview.exercise_type) {
            case 'multiple_choice':
                return (
                    <div className="space-y-4">
                        <p className="text-xl text-white mb-6">{currentReview.content.question}</p>
                        <div className="grid grid-cols-1 gap-3">
                            {currentReview.content.options.map((option, index) => (
                                <button
                                    key={index}
                                    onClick={() => setSelectedOption(option)}
                                    disabled={showResult}
                                    className={`p-4 rounded-xl text-left transition-all duration-200 ${
                                        selectedOption === option
                                            ? 'bg-blue-500 text-white'
                                            : 'bg-white/20 text-white hover:bg-white/30'
                                    } ${showResult && option === currentReview.content.correct_answer ? 'bg-green-500' : ''}
                                    ${showResult && selectedOption === option && option !== currentReview.content.correct_answer ? 'bg-red-500' : ''}`}
                                >
                                    {option}
                                </button>
                            ))}
                        </div>
                    </div>
                );

            case 'translation':
                return (
                    <div className="space-y-6">
                        <p className="text-xl text-white mb-4">Traduisez cette phrase :</p>
                        <div className="bg-white/20 p-4 rounded-xl">
                            <p className="text-white text-lg">{currentReview.content.text_to_translate}</p>
                        </div>
                        <input
                            type="text"
                            value={userAnswer}
                            onChange={(e) => setUserAnswer(e.target.value)}
                            disabled={showResult}
                            placeholder="Votre traduction..."
                            className="w-full p-4 rounded-xl bg-white/20 text-white placeholder-white/60 border-2 border-transparent focus:border-white/50 focus:outline-none"
                        />
                    </div>
                );

            case 'fill_blank':
                return (
                    <div className="space-y-6">
                        <p className="text-xl text-white mb-4">Complétez la phrase :</p>
                        <div className="bg-white/20 p-4 rounded-xl">
                            <p className="text-white text-lg">{currentReview.content.sentence}</p>
                        </div>
                        <input
                            type="text"
                            value={userAnswer}
                            onChange={(e) => setUserAnswer(e.target.value)}
                            disabled={showResult}
                            placeholder="Votre réponse..."
                            className="w-full p-4 rounded-xl bg-white/20 text-white placeholder-white/60 border-2 border-transparent focus:border-white/50 focus:outline-none"
                        />
                    </div>
                );

            default:
                return (
                    <div className="text-white">
                        <p>Type d'exercice non supporté: {currentReview.exercise_type}</p>
                    </div>
                );
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-pink-600 p-4">
            {/* Header */}
            <div className="max-w-4xl mx-auto mb-8">
                <div className="flex items-center justify-between bg-white/10 backdrop-blur-lg rounded-2xl p-4">
                    <div className="flex items-center space-x-4">
                        <button
                            onClick={() => navigate(`/learn/${language}`)}
                            className="text-white hover:text-white/80 text-2xl"
                        >
                            ←
                        </button>
                        <div className="flex items-center space-x-2">
                            <span className="text-3xl">{languageFlags[language]}</span>
                            <h1 className="text-2xl font-bold text-white">Révisions {languageNames[language]}</h1>
                        </div>
                    </div>
                    <div className="text-white text-right">
                        <div className="text-sm opacity-80">Score</div>
                        <div className="text-xl font-bold">{score} pts</div>
                    </div>
                </div>
            </div>

            {/* Progress */}
            <div className="max-w-4xl mx-auto mb-8">
                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-4">
                    <div className="flex justify-between text-white mb-2">
                        <span>Progression</span>
                        <span>{currentIndex + 1} / {reviews.length}</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-3">
                        <div 
                            className="bg-gradient-to-r from-green-400 to-blue-500 h-3 rounded-full transition-all duration-300"
                            style={{ width: `${((currentIndex + 1) / reviews.length) * 100}%` }}
                        ></div>
                    </div>
                </div>
            </div>

            {/* Exercise */}
            <div className="max-w-4xl mx-auto">
                <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8">
                    <div className="mb-6">
                        <div className="flex items-center justify-between mb-4">
                            <h2 className="text-2xl font-bold text-white">{currentReview.title}</h2>
                            <div className="flex items-center space-x-2">
                                {currentReview.is_new && (
                                    <span className="bg-yellow-500/80 text-white px-3 py-1 rounded-full text-sm">
                                        Nouveau
                                    </span>
                                )}
                                <span className="bg-white/20 text-white px-3 py-1 rounded-full text-sm">
                                    {currentReview.points} pts
                                </span>
                                {currentReview.success_rate > 0 && (
                                    <span className="bg-blue-500/80 text-white px-3 py-1 rounded-full text-sm">
                                        {currentReview.success_rate}% réussite
                                    </span>
                                )}
                            </div>
                        </div>
                    </div>

                    {renderExerciseContent()}

                    {showResult && (
                        <div className={`mt-6 p-4 rounded-xl ${isCorrect ? 'bg-green-500/20' : 'bg-red-500/20'}`}>
                            <div className="flex items-center space-x-2 mb-2">
                                <span className="text-2xl">{isCorrect ? '✅' : '❌'}</span>
                                <span className="text-white font-bold">
                                    {isCorrect ? 'Correct !' : 'Incorrect'}
                                </span>
                            </div>
                            {!isCorrect && (
                                <p className="text-white/80">
                                    Réponse correcte : {currentReview.content.correct_answer}
                                </p>
                            )}
                            {currentReview.content.explanation && (
                                <p className="text-white/80 mt-2">{currentReview.content.explanation}</p>
                            )}
                        </div>
                    )}

                    <div className="mt-8 flex justify-center">
                        {!showResult ? (
                            <button
                                onClick={submitAnswer}
                                disabled={!userAnswer && !selectedOption}
                                className="bg-green-500 hover:bg-green-600 disabled:bg-gray-500 disabled:cursor-not-allowed text-white font-bold py-4 px-8 rounded-xl text-lg transition-all duration-200"
                            >
                                Vérifier
                            </button>
                        ) : (
                            <button
                                onClick={nextReview}
                                className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-4 px-8 rounded-xl text-lg transition-all duration-200"
                            >
                                {currentIndex < reviews.length - 1 ? 'Suivant' : 'Terminer'}
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Review;
